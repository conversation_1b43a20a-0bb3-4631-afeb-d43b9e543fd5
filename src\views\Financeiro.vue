<template>
  <lumi-sidenav
    icon="mdi-currency-usd"
    class="fixed-end lumi-sidenav"
    v-if="showSidenav"
    :config="sidenavConfig"
    @action="handleSidenavAction"
  ></lumi-sidenav>

  <div class="main-page-content">
    <div class="py-4 container-fluid">
      <!-- Navegação por abas -->
      <div class="row mb-4">
        <div class="col-12">
          <div class="nav-wrapper position-relative end-0">
            <ul class="nav nav-pills nav-fill p-1" role="tablist">
              <li class="nav-item">
                <a class="nav-link mb-0 px-0 py-1"
                   :class="{ active: activeTab === 'analitico' }"
                   @click="activeTab = 'analitico'"
                   data-bs-toggle="tab"
                   href="#analitico"
                   role="tab"
                   aria-selected="true">
                  <i class="fas fa-list me-2"></i>
                  <PERSON><PERSON><PERSON> Detalhada
                </a>
              </li>
              <li class="nav-item">
                <a class="nav-link mb-0 px-0 py-1"
                   :class="{ active: activeTab === 'sintetico' }"
                   @click="activeTab = 'sintetico'"
                   data-bs-toggle="tab"
                   href="#sintetico"
                   role="tab"
                   aria-selected="false">
                  <i class="fas fa-chart-pie me-2"></i>
                  Dashboard Executivo
                </a>
              </li>
            </ul>
          </div>
        </div>
      </div>

      <!-- Conteúdo das abas -->
      <div class="tab-content" id="tabs-tabContent">
        <!-- Aba Gestão Detalhada (Analítico) -->
        <div class="tab-pane fade"
             :class="{ 'show active': activeTab === 'analitico' }"
             id="analitico"
             role="tabpanel">
          <financeiro-analitico
            :faturas="faturas"
            :loading="loading.faturas"
            :estatisticas="estatisticas"
            @refresh="loadFaturas"
            @create="openCreateCompleteModal"
            @edit="openEditModal"
            @delete="deleteFatura"
            @mark-paid="markAsPaid"
          />
        </div>

        <!-- Aba Dashboard Executivo (Sintético) -->
        <div class="tab-pane fade"
             :class="{ 'show active': activeTab === 'sintetico' }"
             id="sintetico"
             role="tabpanel">
          <financeiro-sintetico
            :estatisticas="estatisticas"
            :loading="loading.estatisticas"
            @refresh="loadEstatisticas"
          />
        </div>
      </div>
    </div>
  </div>

  <!-- Modal para criar/editar fatura -->
  <financeiro-modal
    ref="financeiroModal"
    @saved="onFaturaSaved"
  />

  <!-- Modal de Criação Completo -->
  <financeiro-create-modal
    ref="financeiroCreateModal"
    @saved="onFaturaSaved"
  />
</template>
<script>
import { mapState, mapMutations } from "vuex";
import LumiSidenav from "@/views/components/LumiSidenav/index.vue";
import FinanceiroAnalitico from "@/components/Financeiro/FinanceiroAnalitico.vue";
import FinanceiroSintetico from "@/components/Financeiro/FinanceiroSintetico.vue";
import FinanceiroModal from "@/components/Financeiro/FinanceiroModal.vue";
import FinanceiroCreateModal from "@/components/Financeiro/FinanceiroCreateModal.vue";
import { financeiroService } from "@/services/financeiroService";
import cSwal from "@/utils/cSwal.js";

export default {
  name: "Financeiro",
  components: {
    LumiSidenav,
    FinanceiroAnalitico,
    FinanceiroSintetico,
    FinanceiroModal,
    FinanceiroCreateModal,
  },
  data() {
    return {
      activeTab: 'analitico',
      faturas: [],
      estatisticas: {},
      loading: {
        faturas: false,
        estatisticas: false,
      },
      sidenavConfig: {
        groups: [
          {
            title: "FINANCEIRO",
            buttons: [
              {
                text: "Nova Fatura",
                icon: "add",
                iconType: "material",
                action: "newFatura",
                attributes: {
                  "data-bs-toggle": "modal",
                  "data-bs-target": "#modalFinanceiro"
                }
              },
              {
                text: "Relatório Mensal",
                icon: "assessment",
                iconType: "material",
                action: "monthlyReport"
              },
              {
                text: "Exportar Dados",
                icon: "download",
                iconType: "material",
                action: "exportData"
              }
            ]
          }
        ]
      }
    };
  },
  computed: {
    ...mapState(["showSidenav"]),
  },
  methods: {
    ...mapMutations(["navbarMinimize"]),

    handleSidenavAction(action, button) {
      console.log(`Action: ${action}`, button);

      switch (action) {
        case 'newFatura':
          this.openCreateModal();
          break;
        case 'monthlyReport':
          this.generateMonthlyReport();
          break;
        case 'exportData':
          this.exportData();
          break;
      }
    },

    async loadFaturas() {
      this.loading.faturas = true;
      try {
        const response = await financeiroService.getFaturas();
        this.faturas = response.data.data.data || [];
      } catch (error) {
        console.error('Erro ao carregar faturas:', error);
        cSwal.cError('Erro ao carregar faturas');
      } finally {
        this.loading.faturas = false;
      }
    },

    async loadEstatisticas() {
      this.loading.estatisticas = true;
      try {
        const response = await financeiroService.getEstatisticas();
        this.estatisticas = response.data.data;
      } catch (error) {
        console.error('Erro ao carregar estatísticas:', error);
        cSwal.cError('Erro ao carregar estatísticas');
      } finally {
        this.loading.estatisticas = false;
      }
    },

    openCreateModal() {
      this.$refs.financeiroModal.openCreate();
    },

    openCreateCompleteModal() {
      this.$refs.financeiroCreateModal.open('fatura');
    },

    openEditModal(fatura) {
      this.$refs.financeiroModal.openEdit(fatura);
    },

    async deleteFatura(faturaId) {
      if (confirm('Tem certeza que deseja cancelar esta fatura?')) {
        try {
          await financeiroService.deleteFatura(faturaId);
          cSwal.cSuccess('Fatura cancelada com sucesso');
          this.loadFaturas();
        } catch (error) {
          console.error('Erro ao cancelar fatura:', error);
          cSwal.cError('Erro ao cancelar fatura');
        }
      }
    },

    async markAsPaid(faturaId, paymentData) {
      try {
        await financeiroService.markAsPaid(faturaId, paymentData);
        cSwal.cSuccess('Fatura marcada como paga');
        this.loadFaturas();
        this.loadEstatisticas();
      } catch (error) {
        console.error('Erro ao marcar fatura como paga:', error);
        cSwal.cError('Erro ao marcar fatura como paga');
      }
    },

    onFaturaSaved() {
      this.loadFaturas();
      this.loadEstatisticas();
    },

    generateMonthlyReport() {
      // TODO: Implementar geração de relatório mensal
      cSwal.cInfo('Funcionalidade em desenvolvimento');
    },

    exportData() {
      // TODO: Implementar exportação de dados
      cSwal.cInfo('Funcionalidade em desenvolvimento');
    },
  },

  async mounted() {
    await Promise.all([
      this.loadFaturas(),
      this.loadEstatisticas()
    ]);
  },
};
</script>
