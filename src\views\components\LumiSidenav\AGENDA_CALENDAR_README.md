# Calendário da Sidenav - Tela da Agenda

## Visão Geral

Esta implementação adiciona um calendário elegante e funcional na sidenav da tela da Agenda, substituindo o header padrão (ícone + título) por uma interface de calendário interativa.

## Componentes Modificados

### 1. LumiSidenav/index.vue
- **Mudança**: Adicionado suporte a slot customizado `#header`
- **Funcionalidade**: Permite substituir o header padrão da sidenav por conteúdo customizado
- **Uso**: `<template #header><!-- conteúdo customizado --></template>`

### 2. LumiSidenav/SidenavCalendar.vue (NOVO)
- **Componente**: Calendário compacto para uso na sidenav
- **Características**:
  - Input de data estilizado na parte superior
  - Calendário mensal com navegação por setas
  - Indicadores visuais para dias com eventos
  - Seleção de data com feedback visual
  - Animações suaves para transições
  - Design responsivo e elegante

### 3. views/Agenda.vue
- **Mudanças**:
  - Integração do SidenavCalendar no slot header da sidenav
  - Método `onSidenavDateSelected()` para sincronização de datas
  - Desabilitação do `nativeDatepicker` (removido input redundante)
  - Sincronização bidirecional entre calendário da sidenav e agenda principal

### 4. LumiCalendar/components/calendar/core-index.vue
- **Melhorias nos botões de navegação**:
  - Botões quadrados (8px border-radius) em vez de circulares
  - Tamanho aumentado: 44x44px (era 36x36px)
  - Efeitos hover aprimorados com gradientes
  - Animações mais suaves e elegantes
  - Melhor feedback visual ao clicar

## Funcionalidades Implementadas

### ✅ Calendário na Sidenav
- Calendário compacto e elegante na sidenav da Agenda
- Input de data formatado em português
- Navegação mensal com botões estilizados
- Indicadores visuais para dias com consultas

### ✅ Sincronização de Datas
- Seleção de data no calendário da sidenav atualiza a agenda principal
- Mudanças na agenda principal refletem no calendário da sidenav
- Carregamento automático de consultas ao alterar data

### ✅ Melhorias nos Botões de Navegação
- Botões anterior/próximo redesenhados (quadrados, maiores)
- Efeitos hover com gradientes e transformações
- Melhor acessibilidade e facilidade de clique

### ✅ Remoção de Redundância
- Input de data removido da agenda principal
- Interface mais limpa e organizada

### ✅ Animações e Polimentos
- Transições suaves para mudanças de mês
- Animações de seleção de data
- Efeitos hover elegantes
- Feedback visual ao interagir

## Como Usar

### Na Tela da Agenda
O calendário aparece automaticamente na sidenav quando a tela da Agenda é carregada. Os usuários podem:

1. **Navegar pelos meses**: Usar as setas < > no header do calendário
2. **Selecionar data**: Clicar em qualquer dia do mês atual
3. **Ver consultas**: Dias com consultas têm um indicador verde
4. **Input de data**: Clicar no campo de data para abrir o seletor nativo

### Integração com Outras Telas
Para usar o calendário da sidenav em outras telas:

```vue
<lumi-sidenav :config="sidenavConfig" @action="handleAction">
  <template #header>
    <sidenav-calendar
      :selected-date="selectedDate"
      :events="events"
      @date-selected="onDateSelected"
    />
  </template>
</lumi-sidenav>
```

## Propriedades do SidenavCalendar

| Prop | Tipo | Padrão | Descrição |
|------|------|--------|-----------|
| `selectedDate` | Date | `new Date()` | Data atualmente selecionada |
| `events` | Array | `[]` | Lista de eventos para mostrar indicadores |

## Eventos Emitidos

| Evento | Payload | Descrição |
|--------|---------|-----------|
| `date-selected` | Date | Emitido quando uma data é selecionada |

## Estilos e Temas

O componente usa variáveis CSS consistentes com o design system da aplicação:
- Cores primárias: `#0ea5e9`, `#0284c7`
- Cores de texto: `#374151`, `#6b7280`
- Bordas: `#E0E0E0`
- Sombras suaves para profundidade
- Gradientes para elementos interativos

## Responsividade

O calendário é totalmente responsivo e se adapta a diferentes tamanhos de tela, mantendo a usabilidade em dispositivos móveis.
