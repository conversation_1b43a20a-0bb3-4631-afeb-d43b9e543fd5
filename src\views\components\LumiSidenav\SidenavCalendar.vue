<template>
  <div class="sidenav-calendar-wrapper">
    <!-- Input de data -->
    <div class="date-input-container mb-3">
      <input
        type="date"
        v-model="selectedDateString"
        @change="onDateInputChange"
        class="date-input"
        :title="formatDateForDisplay(selectedDate)"
      />
      <div class="date-display">
        {{ formatDateForDisplay(selectedDate) }}
      </div>
    </div>

    <!-- Calendário compacto -->
    <div class="compact-calendar">
      <!-- Header do calendário com navegação -->
      <div class="calendar-header">
        <button 
          @click="previousMonth" 
          class="nav-btn"
          :title="`Ir para ${getMonthName(getPreviousMonth())}`"
        >
          <i class="material-icons">chevron_left</i>
        </button>
        
        <div class="month-year">
          <Transition name="month-fade" mode="out-in">
            <span :key="`${currentMonth}-${currentYear}`">
              {{ getMonthName(currentMonth) }} {{ currentYear }}
            </span>
          </Transition>
        </div>
        
        <button 
          @click="nextMonth" 
          class="nav-btn"
          :title="`Ir para ${getMonthName(getNextMonth())}`"
        >
          <i class="material-icons">chevron_right</i>
        </button>
      </div>

      <!-- Dias da semana -->
      <div class="weekdays">
        <div class="weekday" v-for="day in weekdays" :key="day">{{ day }}</div>
      </div>

      <!-- Dias do mês -->
      <Transition name="calendar-grid-fade" mode="out-in">
        <div class="days-grid" :key="`${currentMonth}-${currentYear}`">
          <button
            v-for="day in calendarDays"
            :key="`${day.date}-${day.isCurrentMonth}`"
            @click="selectDate(day, $event)"
            class="day-btn"
            :class="{
              'other-month': !day.isCurrentMonth,
              'today': day.isToday,
              'selected': day.isSelected,
              'has-events': day.hasEvents
            }"
            :disabled="!day.isCurrentMonth"
            :title="day.isCurrentMonth ? formatDateForDisplay(day.date) : ''"
          >
            {{ day.date.getDate() }}
            <div v-if="day.hasEvents && day.isCurrentMonth" class="event-indicator"></div>
          </button>
        </div>
      </Transition>
    </div>
  </div>
</template>

<script>
import moment from 'moment';

// Configurar moment para português
moment.locale('pt-br');

export default {
  name: 'SidenavCalendar',
  props: {
    selectedDate: {
      type: Date,
      default: () => new Date()
    },
    events: {
      type: Array,
      default: () => []
    }
  },
  emits: ['date-selected'],
  data() {
    return {
      currentMonth: new Date().getMonth(),
      currentYear: new Date().getFullYear(),
      weekdays: ['D', 'S', 'T', 'Q', 'Q', 'S', 'S']
    };
  },
  computed: {
    selectedDateString: {
      get() {
        return this.dateToString(this.selectedDate);
      },
      set(value) {
        if (value) {
          const [year, month, day] = value.split('-').map(Number);
          const newDate = new Date(year, month - 1, day);
          this.$emit('date-selected', newDate);
        }
      }
    },
    calendarDays() {
      const days = [];
      const firstDay = new Date(this.currentYear, this.currentMonth, 1);
      const lastDay = new Date(this.currentYear, this.currentMonth + 1, 0);
      
      // Ajustar para começar na segunda-feira (1 = segunda)
      const startDate = new Date(firstDay);
      const dayOfWeek = firstDay.getDay();
      const daysToSubtract = dayOfWeek === 0 ? 6 : dayOfWeek - 1;
      startDate.setDate(firstDay.getDate() - daysToSubtract);
      
      // Gerar 42 dias (6 semanas)
      for (let i = 0; i < 42; i++) {
        const date = new Date(startDate);
        date.setDate(startDate.getDate() + i);
        
        const isCurrentMonth = date.getMonth() === this.currentMonth;
        const isToday = this.isSameDay(date, new Date());
        const isSelected = this.isSameDay(date, this.selectedDate);
        const hasEvents = this.hasEventsOnDate(date);
        
        days.push({
          date,
          isCurrentMonth,
          isToday,
          isSelected,
          hasEvents
        });
      }
      
      return days;
    }
  },
  watch: {
    selectedDate: {
      handler(newDate) {
        if (newDate) {
          this.currentMonth = newDate.getMonth();
          this.currentYear = newDate.getFullYear();
        }
      },
      immediate: true
    }
  },
  methods: {
    dateToString(date) {
      if (!date) return '';
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    },
    
    formatDateForDisplay(date) {
      if (!date) return '';
      return moment(date).format('dddd, D [de] MMMM [de] YYYY');
    },
    
    isSameDay(date1, date2) {
      if (!date1 || !date2) return false;
      return date1.getDate() === date2.getDate() &&
             date1.getMonth() === date2.getMonth() &&
             date1.getFullYear() === date2.getFullYear();
    },
    
    hasEventsOnDate(date) {
      if (!this.events || this.events.length === 0) return false;
      
      return this.events.some(event => {
        const eventDate = new Date(event.date);
        return this.isSameDay(eventDate, date);
      });
    },
    
    selectDate(day, event) {
      if (!day.isCurrentMonth) return;

      // Adicionar uma pequena animação visual
      if (event) {
        const button = event.target.closest('.day-btn');
        if (button) {
          button.style.transform = 'scale(0.95)';
          setTimeout(() => {
            button.style.transform = '';
          }, 150);
        }
      }

      this.$emit('date-selected', day.date);
    },
    
    onDateInputChange() {
      // O v-model já cuida da emissão do evento
    },
    
    previousMonth() {
      if (this.currentMonth === 0) {
        this.currentMonth = 11;
        this.currentYear--;
      } else {
        this.currentMonth--;
      }
    },
    
    nextMonth() {
      if (this.currentMonth === 11) {
        this.currentMonth = 0;
        this.currentYear++;
      } else {
        this.currentMonth++;
      }
    },
    
    getPreviousMonth() {
      if (this.currentMonth === 0) {
        return new Date(this.currentYear - 1, 11, 1);
      } else {
        return new Date(this.currentYear, this.currentMonth - 1, 1);
      }
    },
    
    getNextMonth() {
      if (this.currentMonth === 11) {
        return new Date(this.currentYear + 1, 0, 1);
      } else {
        return new Date(this.currentYear, this.currentMonth + 1, 1);
      }
    },
    
    getMonthName(date) {
      const months = [
        'Janeiro', 'Fevereiro', 'Março', 'Abril', 'Maio', 'Junho',
        'Julho', 'Agosto', 'Setembro', 'Outubro', 'Novembro', 'Dezembro'
      ];
      return months[date.getMonth()];
    }
  }
};
</script>

<style scoped>
.sidenav-calendar-wrapper {
  padding: 1rem;
  width: 100%;
}

.date-input-container {
  position: relative;
  margin-bottom: 1rem;
}

.date-input {
  position: absolute;
  opacity: 0;
  pointer-events: none;
  width: 100%;
  height: 100%;
}

.date-display {
  background: white;
  border: 1px solid #E0E0E0;
  border-radius: 8px;
  padding: 0.75rem;
  text-align: center;
  font-size: 0.875rem;
  font-weight: 500;
  color: #344767;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.date-display:hover {
  border-color: #0ea5e9;
  box-shadow: 0 4px 8px rgba(14, 165, 233, 0.1);
}

.compact-calendar {
  background: white;
  border-radius: 12px;
  padding: 1rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border: 1px solid #E0E0E0;
}

.calendar-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1rem;
}

.nav-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  color: #6b7280;
}

.nav-btn:hover {
  background: linear-gradient(135deg, #f3f4f6, #e5e7eb);
  color: #0ea5e9;
  transform: scale(1.1);
  box-shadow: 0 4px 8px rgba(14, 165, 233, 0.2);
}

.nav-btn:active {
  transform: scale(0.95);
}

.nav-btn i {
  font-size: 1.25rem;
}

.month-year {
  font-weight: 600;
  font-size: 1rem;
  color: #374151;
}

.weekdays {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 0.25rem;
  margin-bottom: 0.5rem;
}

.weekday {
  text-align: center;
  font-size: 0.75rem;
  font-weight: 600;
  color: #6b7280;
  padding: 0.5rem 0;
}

.days-grid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 0.25rem;
}

.day-btn {
  position: relative;
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
  transition: all 0.2s ease;
  min-height: 2.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.day-btn:hover:not(:disabled) {
  background: #f3f4f6;
}

.day-btn.other-month {
  color: #d1d5db;
  cursor: not-allowed;
}

.day-btn.today {
  background: #fef3c7;
  color: #d97706;
  font-weight: 700;
}

.day-btn.selected {
  background: linear-gradient(to right, #0ea5e9, #0284c7);
  color: white;
  font-weight: 700;
  box-shadow: 0 4px 8px rgba(14, 165, 233, 0.3);
}

.day-btn.has-events::after {
  content: '';
  position: absolute;
  bottom: 0.25rem;
  right: 0.25rem;
  width: 0.375rem;
  height: 0.375rem;
  background: #10b981;
  border-radius: 50%;
}

.day-btn.selected.has-events::after {
  background: rgba(255, 255, 255, 0.8);
}

.event-indicator {
  position: absolute;
  bottom: 0.25rem;
  right: 0.25rem;
  width: 0.375rem;
  height: 0.375rem;
  background: #10b981;
  border-radius: 50%;
}

/* Animações */
.month-fade-enter-active,
.month-fade-leave-active {
  transition: all 0.3s ease;
}

.month-fade-enter-from {
  opacity: 0;
  transform: translateY(-10px);
}

.month-fade-leave-to {
  opacity: 0;
  transform: translateY(10px);
}

.calendar-grid-fade-enter-active,
.calendar-grid-fade-leave-active {
  transition: all 0.4s ease;
}

.calendar-grid-fade-enter-from {
  opacity: 0;
  transform: scale(0.95) translateY(10px);
}

.calendar-grid-fade-leave-to {
  opacity: 0;
  transform: scale(1.05) translateY(-10px);
}

/* Animação para seleção de data */
.day-btn {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.day-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.day-btn.selected {
  animation: selectPulse 0.6s ease-out;
}

@keyframes selectPulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}

/* Animação para o input de data */
.date-display {
  transition: all 0.3s ease;
}

.date-input-container:hover .date-display {
  transform: translateY(-1px);
  box-shadow: 0 6px 12px rgba(14, 165, 233, 0.15);
}
</style>
