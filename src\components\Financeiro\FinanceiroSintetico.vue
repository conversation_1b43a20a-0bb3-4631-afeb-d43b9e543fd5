<template>
  <div class="financeiro-sintetico">
    <!-- Cards de Estatísticas Principais -->
    <div class="row mb-4">
      <div class="col-xl-3 col-sm-6 mb-xl-0 mb-4">
        <div class="card">
          <div class="card-body p-3">
            <div class="row">
              <div class="col-8">
                <div class="numbers">
                  <p class="text-sm mb-0 text-capitalize font-weight-bold">Receita do Mês</p>
                  <h5 class="font-weight-bolder mb-0">
                    {{ formatCurrency(estatisticas.receita_mes || 0) }}
                    <span class="text-success text-sm font-weight-bolder">+15%</span>
                  </h5>
                </div>
              </div>
              <div class="col-4 text-end">
                <div class="icon icon-shape bg-gradient-primary shadow text-center border-radius-md">
                  <i class="ni ni-money-coins text-lg opacity-10" aria-hidden="true"></i>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="col-xl-3 col-sm-6 mb-xl-0 mb-4">
        <div class="card">
          <div class="card-body p-3">
            <div class="row">
              <div class="col-8">
                <div class="numbers">
                  <p class="text-sm mb-0 text-capitalize font-weight-bold">Faturas Pendentes</p>
                  <h5 class="font-weight-bolder mb-0">
                    {{ estatisticas.quantidade_pendente || 0 }}
                    <span class="text-warning text-sm font-weight-bolder">
                      {{ formatCurrency(estatisticas.total_pendente || 0) }}
                    </span>
                  </h5>
                </div>
              </div>
              <div class="col-4 text-end">
                <div class="icon icon-shape bg-gradient-warning shadow text-center border-radius-md">
                  <i class="ni ni-paper-diploma text-lg opacity-10" aria-hidden="true"></i>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="col-xl-3 col-sm-6 mb-xl-0 mb-4">
        <div class="card">
          <div class="card-body p-3">
            <div class="row">
              <div class="col-8">
                <div class="numbers">
                  <p class="text-sm mb-0 text-capitalize font-weight-bold">Faturas Vencidas</p>
                  <h5 class="font-weight-bolder mb-0">
                    {{ estatisticas.quantidade_vencida || 0 }}
                    <span class="text-danger text-sm font-weight-bolder">
                      {{ formatCurrency(estatisticas.total_vencido || 0) }}
                    </span>
                  </h5>
                </div>
              </div>
              <div class="col-4 text-end">
                <div class="icon icon-shape bg-gradient-danger shadow text-center border-radius-md">
                  <i class="ni ni-world text-lg opacity-10" aria-hidden="true"></i>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="col-xl-3 col-sm-6">
        <div class="card">
          <div class="card-body p-3">
            <div class="row">
              <div class="col-8">
                <div class="numbers">
                  <p class="text-sm mb-0 text-capitalize font-weight-bold">Taxa de Inadimplência</p>
                  <h5 class="font-weight-bolder mb-0">
                    {{ calculateInadimplenciaRate() }}%
                    <span class="text-success text-sm font-weight-bolder">-2%</span>
                  </h5>
                </div>
              </div>
              <div class="col-4 text-end">
                <div class="icon icon-shape bg-gradient-success shadow text-center border-radius-md">
                  <i class="ni ni-chart-bar-32 text-lg opacity-10" aria-hidden="true"></i>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Gráficos -->
    <div class="row mb-4">
      <div class="col-lg-7 mb-lg-0 mb-4">
        <div class="card z-index-2">
          <div class="card-header pb-0">
            <h6>Receitas Mensais</h6>
            <p class="text-sm">
              <i class="fa fa-arrow-up text-success"></i>
              <span class="font-weight-bold">+5% a mais</span> em relação ao mês anterior
            </p>
          </div>
          <div class="card-body p-3">
            <div class="chart">
              <canvas id="chart-line" class="chart-canvas" height="300"></canvas>
            </div>
          </div>
        </div>
      </div>
      <div class="col-lg-5">
        <div class="card">
          <div class="card-header pb-0 p-3">
            <h6 class="mb-0">Distribuição por Status</h6>
          </div>
          <div class="card-body p-3">
            <div class="chart">
              <canvas id="chart-pie" class="chart-canvas" height="300"></canvas>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Tabelas de Resumo -->
    <div class="row">
      <div class="col-lg-7 mb-lg-0 mb-4">
        <div class="card">
          <div class="card-header pb-0 p-3">
            <div class="d-flex justify-content-between">
              <h6 class="mb-2">Próximos Vencimentos</h6>
            </div>
          </div>
          <div class="table-responsive">
            <table class="table align-items-center">
              <tbody>
                <tr v-for="fatura in proximosVencimentos" :key="fatura.id">
                  <td class="w-30">
                    <div class="d-flex px-2 py-1 align-items-center">
                      <div class="ms-4">
                        <p class="text-xs font-weight-bold mb-0">{{ fatura.paciente?.nome }}</p>
                        <h6 class="text-sm mb-0">{{ fatura.descricao }}</h6>
                      </div>
                    </div>
                  </td>
                  <td>
                    <div class="text-center">
                      <p class="text-xs font-weight-bold mb-0">Valor:</p>
                      <h6 class="text-sm mb-0">{{ formatCurrency(fatura.valor_final) }}</h6>
                    </div>
                  </td>
                  <td>
                    <div class="text-center">
                      <p class="text-xs font-weight-bold mb-0">Vencimento:</p>
                      <h6 class="text-sm mb-0">{{ formatDate(fatura.data_vencimento) }}</h6>
                    </div>
                  </td>
                  <td class="align-middle text-sm">
                    <div class="col text-center">
                      <span class="badge badge-sm" :class="getStatusBadgeClass(fatura.status)">
                        {{ getStatusText(fatura.status) }}
                      </span>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
      <div class="col-lg-5">
        <div class="card">
          <div class="card-header pb-0 p-3">
            <h6 class="mb-0">Últimos Pagamentos</h6>
          </div>
          <div class="card-body p-3">
            <ul class="list-group">
              <li class="list-group-item border-0 d-flex justify-content-between ps-0 mb-2 border-radius-lg" 
                  v-for="pagamento in ultimosPagamentos" :key="pagamento.id">
                <div class="d-flex align-items-center">
                  <div class="icon icon-shape icon-sm me-3 bg-gradient-success shadow text-center">
                    <i class="ni ni-check-bold text-white opacity-10"></i>
                  </div>
                  <div class="d-flex flex-column">
                    <h6 class="mb-1 text-dark text-sm">{{ pagamento.paciente?.nome }}</h6>
                    <span class="text-xs">{{ formatDate(pagamento.data_pagamento) }}</span>
                  </div>
                </div>
                <div class="d-flex align-items-center text-success text-gradient text-sm font-weight-bold">
                  {{ formatCurrency(pagamento.valor_final) }}
                </div>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>

    <!-- Métricas Adicionais -->
    <div class="row mt-4">
      <div class="col-lg-4 col-md-6 mt-4">
        <div class="card">
          <div class="card-body">
            <h6 class="mb-0">Ticket Médio</h6>
            <p class="text-sm">{{ formatCurrency(calculateTicketMedio()) }}</p>
            <div class="progress">
              <div class="progress-bar bg-gradient-success" role="progressbar" style="width: 60%"></div>
            </div>
          </div>
        </div>
      </div>
      <div class="col-lg-4 col-md-6 mt-4">
        <div class="card">
          <div class="card-body">
            <h6 class="mb-0">Tempo Médio de Recebimento</h6>
            <p class="text-sm">{{ calculateTempoMedioRecebimento() }} dias</p>
            <div class="progress">
              <div class="progress-bar bg-gradient-info" role="progressbar" style="width: 45%"></div>
            </div>
          </div>
        </div>
      </div>
      <div class="col-lg-4 col-md-6 mt-4">
        <div class="card">
          <div class="card-body">
            <h6 class="mb-0">Crescimento Mensal</h6>
            <p class="text-sm">+{{ calculateCrescimentoMensal() }}%</p>
            <div class="progress">
              <div class="progress-bar bg-gradient-warning" role="progressbar" style="width: 75%"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { financeiroService } from '@/services/financeiroService';

export default {
  name: 'FinanceiroSintetico',
  props: {
    estatisticas: {
      type: Object,
      default: () => ({})
    },
    loading: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      proximosVencimentos: [],
      ultimosPagamentos: [],
      chartLine: null,
      chartPie: null
    };
  },
  methods: {
    formatCurrency: financeiroService.formatCurrency,
    formatDate: financeiroService.formatDate,
    getStatusBadgeClass: financeiroService.getStatusBadgeClass,
    getStatusText: financeiroService.getStatusText,

    calculateInadimplenciaRate() {
      const total = this.estatisticas.total_geral || 0;
      const vencido = this.estatisticas.total_vencido || 0;
      
      if (total === 0) return 0;
      return ((vencido / total) * 100).toFixed(1);
    },

    calculateTicketMedio() {
      const totalFaturas = (this.estatisticas.quantidade_paga || 0) + 
                          (this.estatisticas.quantidade_pendente || 0) + 
                          (this.estatisticas.quantidade_vencida || 0);
      const totalValor = this.estatisticas.total_geral || 0;
      
      if (totalFaturas === 0) return 0;
      return totalValor / totalFaturas;
    },

    calculateTempoMedioRecebimento() {
      // TODO: Implementar cálculo real baseado nos dados
      return 15;
    },

    calculateCrescimentoMensal() {
      // TODO: Implementar cálculo real baseado nos dados históricos
      return 12.5;
    },

    async loadDashboardData() {
      try {
        // TODO: Carregar dados específicos do dashboard
        // const response = await financeiroService.getDashboardData();
        // this.proximosVencimentos = response.data.proximos_vencimentos;
        // this.ultimosPagamentos = response.data.ultimos_pagamentos;
      } catch (error) {
        console.error('Erro ao carregar dados do dashboard:', error);
      }
    },

    initCharts() {
      // TODO: Implementar gráficos usando Chart.js ou similar
      this.$nextTick(() => {
        this.initLineChart();
        this.initPieChart();
      });
    },

    initLineChart() {
      // TODO: Implementar gráfico de linha para receitas mensais
    },

    initPieChart() {
      // TODO: Implementar gráfico de pizza para distribuição por status
    }
  },

  mounted() {
    this.loadDashboardData();
    this.initCharts();
  },

  watch: {
    estatisticas: {
      handler() {
        this.initCharts();
      },
      deep: true
    }
  }
};
</script>

<style scoped>
.financeiro-sintetico .card {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.icon-shape {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.bg-gradient-primary {
  background: linear-gradient(87deg, #5e72e4 0, #825ee4 100%);
}

.bg-gradient-warning {
  background: linear-gradient(87deg, #fb6340 0, #fbb140 100%);
}

.bg-gradient-danger {
  background: linear-gradient(87deg, #f5365c 0, #f56036 100%);
}

.bg-gradient-success {
  background: linear-gradient(87deg, #2dce89 0, #2dcecc 100%);
}

.progress {
  height: 6px;
}

.list-group-item {
  background-color: transparent;
}
</style>
