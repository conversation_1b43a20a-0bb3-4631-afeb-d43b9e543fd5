<template>
  <aside
    id="sidenav-main"
    class="sidenav navbar navbar-vertical navbar-expand-xs rotate-caret fixed-end bg-transparent"
    :style="`background: linear-gradient(180deg, #FFF, #fbfdfe, #fbfdfe) !important; ${customWidth ? `width: ${customWidth} !important;` : ''}`"
  >
    <!-- Custom header slot for special cases (like Agenda calendar) -->
    <slot name="header" v-if="$slots.header"></slot>

    <!-- Default header -->
    <div v-else class="sidenav-header text-center pl-6">
      <v-icon style="font-size: 55pt; color: #5988A8 !important; margin: 0.5rem; margin-top: 0.8rem">{{ icon }}</v-icon>
    </div>

    <div style="width: 100%; height: 1px; background: linear-gradient(90deg, #fbfdfe, #c7d7e0, #fbfdfe) !important;">

    </div>
    <!-- Use the configurable sidenav if config is provided, otherwise use the slot -->
    <lumi-sidenav-config v-if="config" :config="config" @action="handleAction"></lumi-sidenav-config>
    <slot v-else></slot>
  </aside>
</template>

<script>
import logo from "@/assets/img/logo-ct.png";
import logoDark from "@/assets/img/logo-ct-dark.png";
import lumiLogo from "@/assets/img/logos/lumi.png";
import { mapState, mapMutations } from "vuex";
import LumiSidenavConfig from "./LumiSidenavConfig.vue";

export default {
  name: "index",
  props: {
    icon: String,
    /**
     * Configuration object for the sidenav
     * If provided, the sidenav will be rendered using LumiSidenavConfig
     * Otherwise, the default slot will be used
     */
    config: {
      type: Object,
      default: null
    },
    /**
     * Custom width for the sidenav (e.g., "320px", "25rem")
     * If not provided, uses the default width
     */
    customWidth: {
      type: String,
      default: null
    }
  },
  components: {
    LumiSidenavConfig
  },
  data() {
    return {
      logo,
      logoDark,
      lumiLogo,
    };
  },
  computed: {
    ...mapState(["isRTL", "sidebarType", "isDarkMode"]),
  },
  methods: {
    ...mapMutations(["navbarMinimize"]),

    /**
     * Handle action from the sidenav
     * @param {String} action - Action identifier
     * @param {Object} button - Button configuration
     */
    handleAction(action, button) {
      this.$emit('action', action, button);

      // Auto-collapse sidenav after action unless it's a confirmation action
      // or autoCollapse is explicitly set to false
      if (button.autoCollapse !== false && !this.isConfirmationAction(action)) {
        this.collapseSidenav();
      }
    },

    /**
     * Check if an action is a confirmation action that shouldn't auto-collapse
     * @param {String} action - Action identifier
     * @returns {Boolean}
     */
    isConfirmationAction(action) {
      const confirmationActions = ['logout', 'delete', 'remove', 'confirm'];
      return confirmationActions.some(confirmAction =>
        action.toLowerCase().includes(confirmAction)
      );
    },

    /**
     * Collapse the sidenav
     */
    collapseSidenav() {
      // Check if sidenav is open and pinned
      const sidenavElement = document.querySelector(".g-sidenav-show");
      if (sidenavElement && sidenavElement.classList.contains("g-sidenav-pinned")) {
        this.navbarMinimize();
      }
    }
  }
};
</script>
