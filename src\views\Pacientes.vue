<template>
  <lumi-sidenav
    :custom_class="color"
    icon="mdi-account-details"
    class="fixed-end lumi-sidenav"
    v-if="showSidenav"
    :config="sidenavConfig"
    @action="handleSidenavAction"
  ></lumi-sidenav>

  <div class="main-page-content">
    <div class="row">
      <div class="col-12">
        <!-- <div class="container-fluid mb-4">
          <div class="row">
            <div class="col-md-6">
              <div class="card mt-3" style="border: 1px solid #EEE;">
                <div class="card-body px-3">
                  <div class="pb-1 text-start">
                    <p class="mb-0" style="font-weight: 400;">Fichas de avaliação inicial</p>
                  </div>
                  <table class="table-sm w-100 vsm-table">
                    <thead>
                      <tr>
                        <th>Data</th>
                        <th>Paciente</th>
                        <th></th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr>
                        <td>
                          <span class="text-sm font-weight-bold">29/05, às 10:33</span>
                        </td>
                        <td><PERSON><PERSON></td>
                        <td>
                          <button class="btn btn-success btn-sm" title="Adicionar paciente" style="padding: 4px 8px;" data-bs-toggle="modal" data-bs-target="#modalAdicionarPacienteDoFormulario"><i
                              class="fas fa-plus me-1" style="font-size: 7pt"></i><i class="fas fa-user d-none d-md-inline"
                              style="font-size: 10pt"></i></button>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
            <div class="col-md-6">
              <div class="card mt-3" style="border: 1px solid #EEE;">
                <div class="card-body">
                  <div class="pb-1 text-start">
                    <p class="mb-0" style="font-weight: 400;">Últimos pacientes adicionados</p>
                  </div>
                  <table class="table-sm w-100 vsm-table">
                    <thead>
                      <tr>
                        <th>Adicionado em</th>
                        <th>Paciente</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr>
                        <td>
                          <span class="text-sm font-weight-bold">29/05, às 11:58</span>
                        </td>
                        <td>Luca Mathias Sanches</td>
                      </tr>
                      <tr>
                        <td>
                          <span class="text-sm font-weight-bold">25/05, às 16:12</span>
                        </td>
                        <td>Júlia Simões Manzoli</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
        </div> -->

        <div class="w-100 text-center mt-4">
          <input
            type="text"
            class="search-input"
            :placeholder="$t('patients.search')"
            @input="updateList($event.target.value)"
            v-model="search"
          />
        </div>

        <div v-if="isLoading.pacientesList" class="w-100 text-center py-5">
          <div class="spinner-border text-primary" role="status"></div>
        </div>

        <v-table v-if="!isLoading.pacientesList && pacientes.length == 0" class="m-3">
          <tbody>
            <tr>
              <td
                class="bg-gradient-light text-dark text-center"
                style="border-radius: 3px; padding: 2px 20px"
              >
                <span v-if="search == ''">{{ $t('patients.emptyState.noPatients') }}</span>
                <span v-if="search != ''">{{ $t('patients.emptyState.noResults') }}</span>
              </td>
            </tr>
          </tbody>
        </v-table>

        <EasyDataTable
          v-if="pacientes.length > 0"
          :headers="headers"
          :items="pacientes"
          @click-row="openPaciente"
          body-row-class-name="clickable"
          header-item-class-name="table-header-item"
          body-item-class-name="table-body-item"
          :rowsPerPageMessage="$t('patients.table.pagination.patientsPerPage')"
          :rowsOfPageSeparatorMessage="$t('patients.table.pagination.of')"
          :emptyMessage="$t('patients.table.pagination.noResults')"
        >
          <template #header-status="header">
            <div class="text-center w-100">{{ $t('patients.table.headers.status') }}</div>
          </template>
          <template #header-dentista="header">
            <div class="text-center w-100 p-0">{{ $t('patients.table.headers.dentist') }}</div>
          </template>

          <template #item-name="{ nome, data_nascimento, profile_picture_url }">
            <div class="d-flex px-2 py-1">
              <div style="min-width: 40px" class="d-none d-md-block">
                <img :src="profile_picture_url" class="avatar avatar-sm me-3" alt="user1" />
              </div>
              <div class="d-flex flex-column justify-content-center">
                <h6 class="mb-0 text-sm">{{ nome }}</h6>
                <p class="text-xs text-bold mb-0">
                  {{ $filters.howMuchTime(data_nascimento, { prefix: false }) }}
                </p>
              </div>
            </div>
          </template>

          <template #item-data_proxima_consulta="{ proxima_consulta }">
            <div class="d-flex flex-column justify-content-center">
              <p class="text-xs font-weight-bold mb-0">
                {{ $filters.dateDmy(proxima_consulta) }}
              </p>
              <p class="text-xs mb-0">
                <b>{{ $filters.howMuchTime(proxima_consulta) }}</b>
              </p>
            </div>
          </template>

          <template #item-created_at="{ created_at }">
            <div class="d-flex flex-column justify-content-center">
              <p class="text-xs font-weight-bold mb-0">{{ $filters.dateDmy(created_at) }}</p>
              <p class="text-xs mb-0">
                <b>{{ $filters.howMuchTime(created_at, { type: 'date' }) }}</b>
              </p>
            </div>
          </template>

          <template
            #item-status="{ status_tratamento, data_inicio_tratamento, data_final_prevista }"
          >
            <div class="align-middle text-center pe-3">
              <span
                class="badge badge-sm w-100 w-md-70"
                :class="statusClass(status_tratamento)"
                v-if="status_tratamento !== 'ATIVO'"
                >{{ statusText(status_tratamento) }}</span
              >

              <div
                class="d-flex flex-column align-items-center justify-content-center"
                v-if="status_tratamento === 'ATIVO'"
              >
                <div class="progress progress-sm w-100 w-md-70">
                  <div :style="{width: getProgresso(data_inicio_tratamento, data_final_prevista) + '%'}">
                    <div
                      class="progress-bar bg-gradient-success"
                      role="progressbar"
                      :aria-valuenow="0"
                      aria-valuemin="0"
                      aria-valuemax="100"
                    ></div>
                  </div>
                  <span
                    class="progress-value"
                    >{{ getProgresso(data_inicio_tratamento, data_final_prevista) }}%</span>
                </div>
              </div>
            </div>
          </template>

          <template #item-dentista="{ dentista }">
            <div class="w-100 text-center pe-3">
              <span class="text-xs text-dark font-weight-bold text-uppercase">{{ dentista }}</span>
            </div>
          </template>
        </EasyDataTable>
      </div>
    </div>
  </div>

  <div class="modal" tabindex="-1" id="modalAdicionarPacienteDoFormulario">
    <div class="modal-dialog modal-dialog-centered">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title">{{ $t('patients.modals.addPatient.title') }}</h5>
          <button
            type="button"
            class="btn-close"
            data-bs-dismiss="modal"
            aria-label="Close"
          ></button>
        </div>
        <div class="modal-body py-4">
          <div class="d-flex flex-column">
            <button class="btn btn-secondary my-3">
              <i class="fas fa-user me-2"></i>
              {{ $t('patients.modals.addPatient.linkExisting') }}
            </button>
            <button
              class="btn btn-primary my-3"
              data-bs-toggle="modal"
              data-bs-target="#modalNovoPaciente"
            >
              <i class="fas fa-plus me-2"></i>
              {{ $t('patients.modals.addPatient.createNew') }}
            </button>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-default" data-bs-dismiss="modal">
            {{ $t('patients.modals.addPatient.back') }}
          </button>
        </div>
      </div>
    </div>
  </div>

  <div class="modal fade lumi-fade" tabindex="-1" id="modalNovoPaciente" ref="modalNovoPaciente">
    <div class="modal-dialog modal-dialog-centered">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title">{{ $t('patients.modals.newPatient.title') }}</h5>
          <button
            type="button"
            class="btn-close"
            data-bs-dismiss="modal"
            aria-label="Close"
            ref="closeModalNovoPaciente"
          ></button>
        </div>
        <div class="modal-body px-4">
          <div class="row">
            <div class="col-12">
              <label>
                <span class="me-1"><font-awesome-icon :icon="['fas', 'user']" /></span>
                {{ $t('patients.modals.newPatient.fields.name') }}:
              </label>
              <MaterialInput
                type="text"
                class="form-control"
                v-model="novoPaciente.nome"
                ref="nome"
                :input="
                  function ($event) {
                    // Remover números e caracteres especiais, manter apenas letras e espaços
                    $event.target.value = $event.target.value.replace(/[^a-zA-ZÀ-ÿ\s]/g, '');
                    capitalizeAll($event);
                  }
                "
              />
            </div>

            <div class="col-sm-6 mt-3">
              <label>
                <span class="me-1"
                  ><font-awesome-icon :icon="['fas', 'hospital']"
                /></span>
                {{ $t('patients.modals.newPatient.fields.clinic') }}:
              </label>

              <select
                v-if="$user.system_admin && novoPaciente.clinica_id !== 'add'"
                class="form-select"
                aria-label="Default select example"
                v-model="novoPaciente.clinica_id"
                @change="changeClinica"
              >
                <option hidden selected value="">{{ $t('patients.modals.newPatient.fields.select') }}</option>
                <option v-for="clinica in clinicas" :key="clinica.id" :value="clinica.id">
                  {{ clinica.nome }}
                </option>
                <!-- <option value="add">{{ $t('patients.modals.newPatient.fields.newOption') }}</option> -->
              </select>

              <MaterialInput
                v-if="$user.system_admin && novoPaciente.clinica_id == 'add'"
                type="text"
                :placeholder="$t('patients.modals.newPatient.fields.newClinicPlaceholder')"
                ref="novaClinica"
                :input="
                  function ($event) {
                    capitalizeAll($event);
                  }
                "
                v-model="novoPaciente.novaClinica"
              />

              <input
                  v-if="!$user.system_admin"
                  readonly
                  class="form-control"
                  type="text"
                  :value="$clinica?.nome ? $clinica.nome : ''"
                />
            </div>

            <div class="col-sm-6 mt-3">
              <label>
                <span class="me-1"><font-awesome-icon :icon="['fas', 'tooth']" /></span>
                {{ $t('patients.modals.newPatient.fields.dentist') }}:
              </label>

              <select
                v-if="novoPaciente.dentista_id !== 'add'"
                class="form-select"
                aria-label="Default select example"
                v-model="novoPaciente.dentista_id"
              >
                <option hidden selected value="">{{ $t('patients.modals.newPatient.fields.select') }}</option>
                <option
                  v-for="dentista in dentistasFiltrados"
                  :key="dentista.id"
                  :value="dentista.id"
                >
                  {{ dentista.nome }}
                </option>
              </select>
            </div>

            <div
              class="col-sm-6 mt-3"
            >
              <label>
                <span class="me-1"><font-awesome-icon :icon="['fas', 'phone']" /></span>
                {{ $t('patients.modals.newPatient.fields.phone') }}:
              </label>
              <MaterialInput
                type="text"
                label=""
                v-model="novoPaciente.celular"
                :mask="phoneMaskWrapper(novoPaciente.celular)"
                placeholder="(##) #####-####"
              />
              <label for="novo-paciente-celular-whatsapp" class="pointer">
                <div class="mt-2">
                  <input
                    type="checkbox"
                    id="novo-paciente-celular-whatsapp"
                    class="mx-1"
                    v-model="novoPaciente.celular_whatsapp"
                  />
                  WhatsApp<i class="fab fa-whatsapp ms-2" style="font-size: 13pt"></i>
                </div>
              </label>
            </div>

            <div class="col-sm-6 mt-3">
              <label>
                <span class="me-1"><font-awesome-icon :icon="['fas', 'globe']" /></span>
                {{ $t('patients.modals.newPatient.fields.language') }}:
              </label>
              <select name="" id="" class="form-select" v-model="novoPaciente.idioma">
                <option value="pt">Português</option>
                <option value="en">Inglês</option>
                <option value="es">Espanhol</option>
              </select>
            </div>

            <div class="col-12 mt-3">
              <label>
                <span class="me-1"><font-awesome-icon :icon="['fas', 'bars']" /></span>
                {{ $t('patients.modals.newPatient.fields.notes') }}:
              </label>
              <textarea
                name=""
                class="form-control"
                rows="5"
                v-model="novoPaciente.observacoes"
              ></textarea>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-primary" @click="_addNovoPaciente">
            {{ $t('patients.modals.newPatient.addButton') }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
const evts = [
  {
    date: "2024-05-04T14:00:00.000Z",
    comment: "",
    id: "cl32rbkjk1700101o53e3e3uhn",
    keywords: "Projet BAMBA",
    name: "MONTCHO Kévin",
  },
  //...
];

const cfg = {
  viewEvent: undefined,
  reportEvent: {
    icon: true,
    text: "",
  },
  searchPlaceholder: "",
  eventName: "",
  closeText: "",
  nativeDatepicker: false,
  todayButton: true,
  firstDayOfWeek: 1,
};

import cSwal from "@/utils/cSwal.js";
import { mapMutations, mapState, mapActions } from "vuex";
import LumiSidenav from "@/views/components/LumiSidenav/index.vue";
import { getDentistas } from "@/services/dentistasService";
import { getClinicas } from "@/services/clinicasService";
import { addNovoPaciente, searchPacientes } from "@/services/pacientesService";
import { phoneMask, capitalizeAll } from "@/helpers/utils.js";
import { closeModalWithAnimation } from "@/utils/modalHelper.js";
import MaterialInput from "@/components/MaterialInput.vue";

const headers = [
  { text: "PACIENTE", value: "name", sortable: true },
  { text: "PRÓXIMA CONSULTA", value: "data_proxima_consulta", sortable: true },
  { text: "STATUS DO TRATAMENTO", value: "status", sortable: true, align: "center" },
  { text: "ORTODONTISTA", value: "dentista", sortable: true },
  { text: "CADASTRADO EM", value: "created_at", sortable: true },
];

var nomeNovoPaciente = "";

var pacientes = [];

var search = "";

var novoPaciente = getNovoPaciente();

function getNovoPaciente() {
  return {
    idioma: "pt",
    clinica_id: "",
    dentista_id: "",
    nome: "",
    celular: "",
    celular_whatsapp: true,
    novaClinica: "",
  };
}

export default {
  name: "tables",
  components: {
    MaterialInput,
    LumiSidenav,
  },
  async created() {
    this.refreshClinicas();
    this.refreshDentistas();
    this.updateList();
  },

  mounted() {
    // Configurar eventos do modal
    const modalElement = document.getElementById('modalNovoPaciente');
    if (modalElement) {
      // Focar no campo de nome quando o modal for aberto
      modalElement.addEventListener("shown.bs.modal", (event) => {
        if (this.$refs.nome) {
          this.$refs.nome.getInput().focus();
        }
      });

      // Resetar o formulário quando o modal for fechado
      modalElement.addEventListener("hidden.bs.modal", (event) => {
        this.resetFormOnModalClose();
        // Remover a classe de fechamento
        modalElement.classList.remove('modal-closing');
      });

      // Adicionar classe para animação de fechamento
      modalElement.addEventListener('hide.bs.modal', () => {
        modalElement.classList.add('modal-closing');
      });

      // Fechar sidenav quando o modal for aberto em telas pequenas
      modalElement.addEventListener('show.bs.modal', () => {
        // Verificar se estamos em uma tela pequena (< 992px - breakpoint md do Bootstrap)
        if (window.innerWidth < 992) {
          this.closeSidenav();
        }
      });
    }
  },
  methods: {
    ...mapMutations(["navbarMinimize"]),

    // Método para fechar a sidenav
    closeSidenav() {
      // Verificar se a sidenav está aberta
      const sidenavElement = document.querySelector(".g-sidenav-show");
      if (sidenavElement && sidenavElement.classList.contains("g-sidenav-pinned")) {
        // Usar o método do Vuex para fechar a sidenav
        this.navbarMinimize();
      }
    },

    handleSidenavAction(action, button) {
      console.log(`Action: ${action}`, button);

      // Implementar as ações da sidenav
      switch (action) {
        case 'newPatient':
          // Modal já é aberto automaticamente pelos atributos data-bs-*
          break;
      }
    },
    async refreshClinicas() {
      if (!this.$user.system_admin)
        return;
      this.clinicas = await getClinicas();
      if (this.$clinica) {
        this.novoPaciente.clinica_id = this.$clinica.id;
      } else if (this.clinicas.length > 0) {
        this.novoPaciente.clinica_id = this.clinicas[0].id;
      }
    },
    async refreshDentistas() {
      this.dentistas = await getDentistas();
      // Verificar se dentistas foi carregado corretamente
      if (!this.dentistas || !Array.isArray(this.dentistas)) {
        this.dentistas = [];
        return;
      }

      if (this.$dentista) {
        this.novoPaciente.dentista_id = this.$dentista.id;
      } else if (this.dentistas.length > 0) {
        this.novoPaciente.dentista_id = this.dentistas[0].id;
      }
    },
    capitalizeAll,
    changeClinica() {
      if (this.novoPaciente.clinica_id == "add") {
        this.$refs.novaClinica.getInput().focus();
      } else {
        // Selecionar automaticamente o primeiro dentista da clínica selecionada
        const dentistasDaClinica = this.dentistasFiltrados;
        if (dentistasDaClinica && Array.isArray(dentistasDaClinica) && dentistasDaClinica.length > 0) {
          this.novoPaciente.dentista_id = dentistasDaClinica[0].id;
        } else {
          this.novoPaciente.dentista_id = "";
        }
      }
    },

    /**
     * Wraps the phoneMask function to pass the length parameter to it.
     * @param {number} length The length of the phone number.
     * @returns {import('vue-the-mask').MaskPattern} The mask object.
     */
    phoneMaskWrapper(length) {
      return phoneMask(length);
    },

    async updateList(search = "") {
      this.isLoading.pacientesList = true;
      this.pacientes = await searchPacientes(search);
      this.isLoading.pacientesList = false;
    },
    statusClass(status) {
      const classMap = {
        "NÃO INICIADO": "bg-gradient-warning",
        CONCLUÍDO: "bg-gradient-success",
        ATIVO: "bg-gradient-secondary",
      };

      return classMap[status] || "";
    },
    async _addNovoPaciente() {
      if (this.$user.system_admin && !this.novoPaciente.clinica_id) {
        cSwal.cAlert(this.$t('patients.alerts.clinicRequired'));
        return false;
      }

      cSwal.loading(this.$t('patients.alerts.addingPatient'));
      const add = await addNovoPaciente(this.novoPaciente);
      cSwal.loaded();

      if (add) {
        // Salvar dados do paciente criado e opção WhatsApp
        const pacienteCriado = {
          nome: this.novoPaciente.nome,
          celular_whatsapp: this.novoPaciente.celular_whatsapp,
          celular: this.novoPaciente.celular,
          id_ficha: add.id_ficha || add.data?.id_ficha || add.id || null,
          public_token: add.public_token || add.data?.public_token || add.token || null
        };

        // Form reset will be handled by success modal actions

        // Fechar o modal com animação
        closeModalWithAnimation('modalNovoPaciente');

        // Fechar a sidenav
        this.closeSidenav();

        // Atualizar lista antes de mostrar modal
        await this.updateList(this.search);

        // Mostrar modal de sucesso refinado
        this.showSuccessModal(pacienteCriado);
      } else {
        cSwal.cError(this.$t('patients.alerts.errorAddingPatient'));
      }

      this.refreshClinicas();
      this.updateList(this.search);
    },
    statusText(status) {
      const textMap = {
        "NÃO INICIADO": this.$t('patients.table.status.notStarted'),
        CONCLUÍDO: this.$t('patients.table.status.completed'),
        ATIVO: this.$t('patients.table.status.inProgress'),
      };

      return textMap[status] || "";
    },

    // Modal de sucesso refinado
    showSuccessModal(paciente) {
      const possuiWhatsapp = paciente.celular_whatsapp && paciente.celular;
      const linkText = possuiWhatsapp ? "Enviar Link da Ficha" : "Copiar Link da Ficha";
      const linkIcon = possuiWhatsapp ? "fab fa-whatsapp" : "fas fa-copy";

      cSwal.fire({
        html: `
          <div style="text-align: center; padding: 20px 0;">
            <h3 style="color: #28a745; font-size: 1.8rem; margin-bottom: 10px; font-weight: 600;">
              ${paciente.nome}
            </h3>
            <p style="color: #6c757d; font-size: 1rem; margin-bottom: 30px;">
              Paciente adicionado com sucesso!
            </p>

            <div style="display: flex; gap: 10px; margin-bottom: 20px; justify-content: center;">
              <button id="btn-abrir-prontuario" class="btn btn-primary" style="flex: 1; max-width: 180px;">
                <i class="fas fa-user-md me-2"></i>Abrir Prontuário
              </button>
              <button id="btn-agendar-consulta" class="btn btn-success" style="flex: 1; max-width: 180px;">
                <i class="fas fa-calendar-plus me-2"></i>Agendar Consulta
              </button>
            </div>

            <button id="btn-link-ficha" class="btn btn-outline-primary" style="width: 100%; max-width: 380px;">
              <i class="${linkIcon} me-2"></i>${linkText}
            </button>

            <button id="btn-fechar-modal" class="btn btn-link text-muted" style="width: 100%; max-width: 380px; margin-top: 20px; font-size: 0.9rem; text-decoration: none;">
              <i class="fas fa-times me-2"></i>Fechar
            </button>
          </div>
        `,
        icon: 'success',
        showConfirmButton: false,
        showCancelButton: false,
        allowOutsideClick: true,
        allowEscapeKey: true,
        customClass: {
          popup: 'swal2-success-modal'
        },
        willClose: () => {
          // Reset form when success modal is closed without action
          this.resetFormAfterSuccess();
        },
        didOpen: () => {
          // Adicionar event listeners aos botões
          const btnProntuario = document.getElementById('btn-abrir-prontuario');
          const btnConsulta = document.getElementById('btn-agendar-consulta');
          const btnLink = document.getElementById('btn-link-ficha');
          const btnFechar = document.getElementById('btn-fechar-modal');

          if (btnProntuario) {
            btnProntuario.addEventListener('click', () => {
              this.abrirProntuario(paciente);
              this.resetFormAfterSuccess();
              cSwal.close();
            });
          }

          if (btnConsulta) {
            btnConsulta.addEventListener('click', () => {
              this.agendarConsulta(paciente);
              this.resetFormAfterSuccess();
              cSwal.close();
            });
          }

          if (btnLink) {
            btnLink.addEventListener('click', () => {
              this.handleLinkFicha(paciente);
              this.resetFormAfterSuccess();
              // Não fechar o modal aqui - deixar aberto para o usuário ver o feedback
            });
          }

          if (btnFechar) {
            btnFechar.addEventListener('click', () => {
              this.resetFormAfterSuccess();
              cSwal.close();
            });
          }
        }
      });
    },

    // Abrir prontuário do paciente
    abrirProntuario(paciente) {
      if (paciente.id_ficha) {
        this.$router.push({
          name: "PacientePadrao",
          params: {
            id_ficha: paciente.id_ficha,
          },
        });
      } else {
        // Tentar encontrar o paciente na lista atualizada
        const pacienteEncontrado = this.pacientes.find(p =>
          p.nome.toLowerCase() === paciente.nome.toLowerCase()
        );

        if (pacienteEncontrado && pacienteEncontrado.id_ficha) {
          this.$router.push({
            name: "PacientePadrao",
            params: {
              id_ficha: pacienteEncontrado.id_ficha,
            },
          });
        } else {
          this.showToast("Paciente criado com sucesso! Atualize a página para acessar o prontuário.", 'info');
        }
      }
    },

    // Agendar consulta
    agendarConsulta(paciente) {
      // Navegar para a página de agenda
      this.$router.push({
        name: "Agenda"
      });
      this.showToast("Redirecionando para a agenda...", 'info');
    },

    // Lidar com link da ficha (WhatsApp ou copiar)
    async handleLinkFicha(paciente) {
      if (!paciente.public_token) {
        // Tentar encontrar o paciente na lista atualizada para obter o token
        const pacienteEncontrado = this.pacientes.find(p =>
          p.nome.toLowerCase() === paciente.nome.toLowerCase()
        );

        if (pacienteEncontrado && pacienteEncontrado.public_token) {
          paciente.public_token = pacienteEncontrado.public_token;
        } else {
          this.showToast("Link da ficha será disponibilizado em breve. Paciente criado com sucesso!", 'info');
          return;
        }
      }

      if (paciente.celular_whatsapp && paciente.celular) {
        this.enviarLinkWhatsApp(paciente);
      } else {
        await this.copiarLinkFicha(paciente);
      }
    },

    // Enviar link via WhatsApp
    enviarLinkWhatsApp(paciente) {
      const phoneNumber = paciente.celular.replace(/\D+/g, "");
      if (phoneNumber.length !== 11) {
        this.showToast("Número de WhatsApp inválido. Por favor, verifique o número.", 'error');
        return;
      }

      const link = this.getFichaInicialLink(paciente.public_token);
      const whatsappLink = `https://wa.me/55${phoneNumber}?text=Olá, bem-vindo à clínica! Por favor, preencha nosso formulário para lhe conhecermos melhor: ${link}`;
      window.open(whatsappLink, "_blank");

      // Não fechar o modal - apenas mostrar feedback
      this.showToast("Link enviado via WhatsApp!", 'success');
    },

    // Copiar link da ficha
    async copiarLinkFicha(paciente) {
      const link = this.getFichaInicialLink(paciente.public_token);

      if (!navigator.clipboard) {
        // Fallback: mostrar modal com link (este modal substitui o atual)
        cSwal.fire({
          title: 'Link da Ficha de Avaliação',
          html: `
            <p>Link para o paciente <strong>${paciente.nome}</strong>:</p>
            <div style="background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0; word-break: break-all;">
              <strong>${link}</strong>
            </div>
            <p><small>Copie o link acima e envie para o paciente.</small></p>
          `,
          icon: 'info',
          confirmButtonText: 'Entendi',
          customClass: {
            popup: 'swal2-link-modal'
          }
        });
        return;
      }

      try {
        await navigator.clipboard.writeText(link);
        // Não fechar o modal - apenas mostrar feedback
        this.showToast("Link copiado para a área de transferência!", 'success');
      } catch (error) {
        console.error("Erro ao copiar link:", error);
        this.showToast("Erro ao copiar link. Tente novamente.", 'error');
      }
    },

    // Gerar link da ficha inicial
    getFichaInicialLink(publicToken) {
      return `${window.location.origin}/bem-vindo/?t=${publicToken}`;
    },

    // Reset form after successful action in success modal
    resetFormAfterSuccess() {
      // Get current clinic selection to preserve it
      const currentClinicId = this.novoPaciente.clinica_id;

      // Reset form to initial state
      this.novoPaciente = getNovoPaciente();

      // Preserve clinic selection for system admin
      if (this.$user.system_admin && currentClinicId) {
        this.novoPaciente.clinica_id = currentClinicId;
      }
    },

    // Reset form when modal is manually closed
    resetFormOnModalClose() {
      // Get current clinic selection to preserve it
      const currentClinicId = this.novoPaciente.clinica_id;

      // Reset form to initial state
      this.novoPaciente = getNovoPaciente();

      // Preserve clinic selection for system admin
      if (this.$user.system_admin && currentClinicId) {
        this.novoPaciente.clinica_id = currentClinicId;
      }
    },

    // Sistema simples de toast notifications
    showToast(message, type = 'info') {
      // Criar elemento do toast
      const toastId = 'toast-' + Date.now();
      const toastHtml = `
        <div id="${toastId}" class="toast fade show position-fixed" style="top: 20px; right: 20px; z-index: 9999; min-width: 300px;">
          <div class="toast-header bg-${type === 'success' ? 'success' : type === 'error' ? 'danger' : 'info'} text-white">
            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'} me-2"></i>
            <strong class="me-auto">${type === 'success' ? 'Sucesso' : type === 'error' ? 'Erro' : 'Informação'}</strong>
            <button type="button" class="btn-close btn-close-white" onclick="document.getElementById('${toastId}').remove()"></button>
          </div>
          <div class="toast-body text-dark">
            ${message}
          </div>
        </div>
      `;

      // Adicionar ao DOM
      document.body.insertAdjacentHTML('beforeend', toastHtml);

      // Remover automaticamente após 4 segundos
      setTimeout(() => {
        const toastElement = document.getElementById(toastId);
        if (toastElement) {
          toastElement.classList.remove('show');
          setTimeout(() => toastElement.remove(), 300);
        }
      }, 4000);
    },
    openPaciente(paciente) {
      this.$router.push({
        name: "PacientePadrao",
        params: {
          id_ficha: paciente.id_ficha,
        },
      });
    },
    getProgresso(data_inicio_tratamento, data_final_prevista) {
      if (!data_inicio_tratamento || !data_final_prevista) return "-";

      const inicio = new Date(data_inicio_tratamento);
      const termino = new Date(data_final_prevista);
      const hoje = new Date();

      if (hoje < inicio) return 0.0;
      if (hoje > termino) return 100.0;

      const duracaoTotal = termino.getTime() - inicio.getTime();
      const duracaoAteHoje = hoje.getTime() - inicio.getTime();

      const progresso = (duracaoAteHoje / duracaoTotal) * 100;

      return parseFloat(progresso.toFixed(1));
    },
  },
  computed: {
    ...mapState([
      "isRTL",
      "color",
      "isAbsolute",
      "isNavFixed",
      "navbarFixed",
      "absolute",
      "showSidenav",
      "showNavbar",
      "showFooter",
      "showConfig",
      "hideConfigButton",
    ]),

    // Filtrar dentistas pela clínica selecionada
    dentistasFiltrados() {
      // Verificar se dentistas existe e é um array
      if (!this.dentistas || !Array.isArray(this.dentistas)) {
        return [];
      }

      if (!this.novoPaciente.clinica_id || this.novoPaciente.clinica_id === "add") {
        return this.dentistas;
      }

      return this.dentistas.filter(dentista => {
        // Verificar se o dentista tem clinica_id diretamente
        if (dentista.clinica_id) {
          return dentista.clinica_id == this.novoPaciente.clinica_id;
        }

        // Verificar se o dentista tem um objeto clinica com id
        if (dentista.clinica && dentista.clinica.id) {
          return dentista.clinica.id == this.novoPaciente.clinica_id;
        }

        return false;
      });
    },
  },
  data() {
    return {
      clinicas: [],
      dentistas: [],
      isLoading: {
        pacientesList: true,
      },
      nomeNovoPaciente,
      headers,
      cfg,
      evts,
      pacientes,
      search,
      novoPaciente,
      sidenavConfig: {
        groups: [
          {
            title: this.$t('patients.sidenav.title'),
            buttons: [
              {
                text: this.$t('patients.sidenav.newPatient'),
                icon: "add",
                iconType: "material",
                action: "newPatient",
                attributes: {
                  "data-bs-toggle": "modal",
                  "data-bs-target": "#modalNovoPaciente"
                }
              }
            ]
          }
        ]
      }
    };
  },
};
</script>

<style scoped>
/* Estilos para o modal de sucesso */
:global(.swal2-success-modal) {
  border-radius: 15px !important;
}

:global(.swal2-success-modal .swal2-icon.swal2-success) {
  border-color: #28a745 !important;
  color: #28a745 !important;
}

:global(.swal2-success-modal .swal2-icon.swal2-success .swal2-success-ring) {
  border-color: #28a745 !important;
}

:global(.swal2-success-modal .swal2-icon.swal2-success) {
  background-color: #28a745 !important;
}

:global(.swal2-success-modal .btn) {
  border-radius: 8px !important;
  font-weight: 500 !important;
  padding: 10px 20px !important;
  transition: all 0.2s ease !important;
}

:global(.swal2-success-modal .btn:hover) {
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 8px rgba(0,0,0,0.15) !important;
}

:global(.swal2-success-modal .btn-link) {
  border: none !important;
  background: none !important;
  box-shadow: none !important;
  padding: 8px 20px !important;
}

:global(.swal2-success-modal .btn-link:hover) {
  transform: none !important;
  box-shadow: none !important;
  background-color: rgba(0,0,0,0.05) !important;
  border-radius: 6px !important;
  text-decoration: none !important;
}

:global(.swal2-link-modal) {
  border-radius: 15px !important;
}

/* Estilos para os toasts */
.toast {
  box-shadow: 0 4px 12px rgba(0,0,0,0.15) !important;
  border-radius: 8px !important;
}

.toast-header {
  border-radius: 8px 8px 0 0 !important;
}

.toast-body {
  background-color: white !important;
  border-radius: 0 0 8px 8px !important;
}
</style>
