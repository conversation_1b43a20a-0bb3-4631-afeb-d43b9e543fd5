export default {
    agenda: 'Agenda',
    badge: 'Badge',
    open: 'Open',
    close: 'Close',
    dismiss: 'Dismiss',
    confirmEdit: {
        ok: 'OK',
        cancel: 'Cancel',
    },
    dataIterator: {
        noResultsText: 'No matching records found',
        loadingText: 'Loading items...',
    },
    dataTable: {
        itemsPerPageText: 'Rows per page:',
        ariaLabel: {
            sortDescending: 'Sorted descending.',
            sortAscending: 'Sorted ascending.',
            sortNone: 'Not sorted.',
            activateNone: 'Activate to remove sorting.',
            activateDescending: 'Activate to sort descending.',
            activateAscending: 'Activate to sort ascending.',
        },
        sortBy: 'Sort by',
    },
    dataFooter: {
        itemsPerPageText: 'Items per page:',
        itemsPerPageAll: 'All',
        nextPage: 'Next page',
        prevPage: 'Previous page',
        firstPage: 'First page',
        lastPage: 'Last page',
        pageText: '{0}-{1} of {2}',
    },
    dateRangeInput: {
        divider: 'to',
    },
    datePicker: {
        itemsSelected: '{0} selected',
        range: {
            title: 'Select dates',
            header: 'Enter dates',
        },
        title: 'Select date',
        header: 'Enter date',
        input: {
            placeholder: 'Enter date',
        },
    },
    noDataText: 'No data available',
    carousel: {
        prev: 'Previous visual',
        next: 'Next visual',
        ariaLabel: {
            delimiter: 'Carousel slide {0} of {1}',
        },
    },
    calendar: {
        moreEvents: '{0} more',
        today: 'Today',
        day: 'Day',
        week: 'Week',
        month: 'Month',
        appointment: 'Appointment{add}',
        view: 'View appointment',
        report: 'Reschedule'
    },
    input: {
        clear: 'Clear {0}',
        prependAction: '{0} prepended action',
        appendAction: '{0} appended action',
        otp: 'Please enter OTP character {0}',
    },
    fileInput: {
        counter: '{0} files',
        counterSize: '{0} files ({1} in total)',
    },
    timePicker: {
        am: 'AM',
        pm: 'PM',
        title: 'Select Time',
    },
    pagination: {
        ariaLabel: {
            root: 'Pagination Navigation',
            next: 'Next page',
            previous: 'Previous page',
            page: 'Go to page {0}',
            currentPage: 'Page {0}, Current page',
            first: 'First page',
            last: 'Last page',
        },
    },
    stepper: {
        next: 'Next',
        prev: 'Previous',
    },
    rating: {
        ariaLabel: {
            item: 'Rating {0} of {1}',
        },
    },
    loading: 'Loading...',
    infiniteScroll: {
        loadMore: 'Load more',
        empty: 'No more',
    },

    // Custom:
    login: {
        submitAction: 'Login'
    },

    // Main Navigation
    mainNav: {
        agenda: 'Schedule',
        patients: 'Patients',
        orthodontists: 'Orthodontists',
        financial: 'Financial',
        settings: 'Settings'
    },

    // Appointment Modal
    appointment: {
        title: {
            new: 'Schedule appointment',
            edit: 'Edit appointment'
        },
        fields: {
            patient: 'Patient',
            orthodontist: 'Orthodontist',
            date: 'Date',
            time: 'Time',
            value: 'Value',
            notes: 'Notes',
            notesPlaceholder: 'Additional information about the appointment or the patient',
            selectPatient: 'Select a patient',
            selectOrthodontist: 'Select an orthodontist',
            createPatient: 'Create new patient',
            new: 'New'
        },
        status: {
            canceled: 'Canceled',
            scheduled: 'Scheduled',
            confirmed: 'Confirmed'
        },
        categories: {
            firstAppointment: 'First appointment',
            assembly: 'Assembly',
            followUp: 'Follow-up',
            activation: 'Activation',
            emergency: 'Emergency',
            removal: 'Removal',
            replanning: 'Replanning',
            postTreatment: 'Post-treatment'
        },
        buttons: {
            cancel: 'Cancel',
            save: 'Save'
        },
        alerts: {
            errorLoadingDentists: 'Error loading the list of orthodontists.',
            errorLoadingPatients: 'Error loading the list of patients.',
            errorLoadingAppointment: 'Error loading appointment data. Please try again.',
            saving: 'Saving appointment...',
            successSaving: 'Appointment saved successfully!',
            errorSaving: 'Error saving the appointment. Please try again.',
            requiredFields: 'Please fill in all required fields.'
        }
    },

    // Profile Settings
    profile: {
        title: 'Profile Settings',
        language: 'Language / Idioma',
        fullName: 'Full Name',
        clinic: 'Clinic',
        email: 'Email',
        username: 'Username',
        newPassword: 'New Password',
        confirmPassword: 'Confirm Password',
        passwordHint: 'Fill only if you want to change your password',
        confirmPasswordHint: 'Confirm your new password',
        saveButton: 'Save Changes',
        savingButton: 'Saving...',
        loadingText: 'Loading...'
    },
    // Navbar
    navbar: {
        search: 'Search here',
        notifications: {
            newMessage: 'New message',
            from: 'from',
            newAlbum: 'New album',
            by: 'by',
            paymentCompleted: 'Payment successfully completed',
            timeIndicators: {
                minutesAgo: 'minutes ago',
                day: 'day',
                days: 'days'
            }
        }
    },

    // Patients
    patients: {
        search: 'Search...',
        emptyState: {
            noPatients: 'There are no registered patients yet.',
            noResults: 'The search did not find any patients.'
        },
        table: {
            headers: {
                patient: 'PATIENT',
                nextAppointment: 'NEXT APPOINTMENT',
                status: 'TREATMENT STATUS',
                dentist: 'ORTHODONTIST',
                registeredAt: 'REGISTERED ON'
            },
            pagination: {
                patientsPerPage: 'Patients per page',
                of: 'of',
                noResults: 'No results'
            },
            status: {
                notStarted: 'NOT STARTED',
                completed: 'COMPLETED',
                inProgress: 'IN PROGRESS'
            }
        },
        modals: {
            newPatient: {
                title: 'New patient',
                addButton: 'Add',
                fields: {
                    name: 'Name',
                    clinic: 'Clinic',
                    dentist: 'Orthodontist',
                    phone: 'Phone',
                    language: 'Language',
                    notes: 'Notes',
                    select: 'Select...',
                    newOption: 'New...',
                    newClinicPlaceholder: 'New clinic name...'
                }
            },
            addPatient: {
                title: 'Add patient',
                linkExisting: 'Link to existing patient',
                createNew: 'Create new patient',
                back: 'Back'
            }
        },
        sidenav: {
            title: 'PATIENTS',
            newPatient: 'New patient'
        },
        alerts: {
            clinicRequired: 'You must select a clinic.',
            addingPatient: 'Adding patient...',
            patientAdded: 'The patient was successfully added!',
            errorAddingPatient: 'An error occurred while trying to add the patient.'
        }
    },
    pendingChanges: {
        title: 'Pending changes',
        patientHasChanges: 'This patient has {count} unsaved {count, plural, one {change} other {changes}}.',
        changesFound: 'Changes found:',
        lastModified: 'Last modified:',
        understood: 'Understood',
        categoryChanges: '{count} {count, plural, one {change} other {changes}}',
        saveChanges: 'Save changes',
        changesSaved: 'Changes saved! Draft saved automatically',
        draftSavedAt: 'Draft saved at {time}'
    }
}