<template>
  <div class="page-width-container">
    <main class="page-width">
      <div class="container-fluid p-0">
        <div class="card no-shadow">
          <div class="card-body p-3">
              <div class="row gx-4 align-items-center flex-wrap">

                <div class="col-12 col-md-5 col-lg-6 d-flex align-items-center justify-content-between">
                  <div class="d-flex align-items-center">
                    <div class="avatar avatar-xl position-relative">
                      <input
                        id="profilePictureFileInput"
                        type="file"
                        accept="image/*"
                        @change="profilePicUpload"
                        hidden
                      />

                      <div class="profile-pic pointer" @click="confirmUpdatePhoto" title="Clique para alterar a foto de perfil">
                        <img
                            v-if="!isLoading.paciente"
                            :src="paciente.profile_picture_url"
                            alt="profile_image"
                            class="shadow-sm w-100 border-radius-lg"
                            @load="profilePictureLoaded"
                          />
                          <div v-else class="spinner-border text-primary" role="status"></div>
                      </div>
                    </div>

                    <div class="ms-3 patient-info">
                      <p class="mb-0 id-ficha" v-if="!isLoading.paciente && paciente.id_ficha !== undefined">#{{ String(paciente.id_ficha).padStart(3, '0') }}</p>
                      <p class="mb-0 id-ficha" v-else><span class="placeholder-text"># -</span></p>
                      <h5 class="mb-0 fs-4 patient-name pointer"
   :class="{'long-name': paciente.nome && paciente.nome.length > 35}"
   @click="confirmChangeName"
   title="Clique para alterar o nome">
   {{ paciente.nome }}
   <font-awesome-icon :icon="['fas', 'edit']" class="edit-icon-hover ms-1" style="font-size: 0.7em;" />
</h5>
                      <p class="mb-0 font-weight-bold">
                        <Transition>
                          <span :key="paciente.data_nascimento">{{
                            $filters.howMuchTime(paciente.data_nascimento, {
                              type: "date",
                              prefix: false,
                            })
                          }}</span>
                        </Transition>
                      </p>
                    </div>
                  </div>
                  <BackButton
                    class="back-btn"
                    :class="{ 'edit-mode': editModeActive }"
                  />
                  <!-- Debug: {{ editModeActive ? 'Em modo de edição' : 'Não está em modo de edição' }} -->
                </div>

                <div class="col-12 col-md-7 col-lg-6 mt-3 mt-lg-0 p-0">
                  <!-- Menu para telas pequenas (sm e abaixo) -->
                  <div class="nav-wrapper position-relative end-0 px-md-2 px-sm-0">
                    <ul class="p-1 bg-transparent nav nav-pills nav-fill menu-2x2" role="tablist">
                      <li class="nav-item perfil" @click="openTab('perfil')">
                        <a
                          class="px-0 py-1 mb-0 nav-link nav-tab d-flex flex-column align-items-center justify-content-center"
                          :class="{ active: activeTab === 'perfil' }"
                          href="javascript:;"
                          role="tab"
                          :aria-selected="activeTab === 'perfil' ? 'true' : 'false'"
                        >
                          <i class="fas fa-user"></i>
                          <span class="mt-1">Perfil</span>
                        </a>
                      </li>
                      <li class="nav-item" @click="openTab('planejamento')">
                        <a
                          class="px-0 py-1 mb-0 nav-link nav-tab d-flex flex-column align-items-center justify-content-center"
                          :class="{ active: activeTab === 'planejamento' }"
                          href="javascript:;"
                          role="tab"
                          :aria-selected="activeTab === 'planejamento' ? 'true' : 'false'"
                        >
                          <i class="fas fa-search"></i>
                          <span class="mt-1">Planejamento</span>
                        </a>
                      </li>
                      <li class="nav-item tratamento" @click="openTab('tratamento')">
                        <a
                          class="px-0 py-1 mb-0 nav-link nav-tab d-flex flex-column align-items-center justify-content-center"
                          :class="{ active: activeTab === 'tratamento' }"
                          href="javascript:;"
                          role="tab"
                          :aria-selected="activeTab === 'tratamento' ? 'true' : 'false'"
                        >
                          <i class="fas fa-teeth-open"></i>
                          <span class="mt-1">Tratamento</span>
                        </a>
                      </li>
                      <li class="nav-item financeiro" @click="openTab('financeiro')">
                        <a
                          class="px-0 py-1 mb-0 nav-link nav-tab d-flex flex-column align-items-center justify-content-center"
                          :class="{ active: activeTab === 'financeiro' }"
                          href="javascript:;"
                          role="tab"
                          :aria-selected="activeTab === 'financeiro' ? 'true' : 'false'"
                        >
                          <i class="fas fa-dollar-sign"></i>
                          <span class="mt-1">Financeiro</span>
                        </a>
                      </li>
                    </ul>
                  </div>
                </div>

              </div>
          </div>
        </div>
      </div>
      <Transition>
        <div class="p-0 container-fluid" v-if="activeTab == 'perfil'">
          <div class="row">
            <div class="col-md-12">
              <Transition>
                <div class="main-container" v-if="activeProfileTab == 'perfilPessoal'">
                  <!-- Mobile Accordion View (sm and below) -->
                  <div class="d-block d-md-none mb-4">
                    <PatientAccordion
                      :paciente="paciente"
                      :isFilledPessoal="isFilledPessoal"
                      :isEditingPessoal="isEditingPessoal"
                      :isFilledResponsavel="isFilledResponsavel"
                      :isEditingResponsavel="isEditingResponsavel"
                      :isFilledEndereco="isFilledEndereco"
                      :isEditingEndereco="isEditingEndereco"
                      :isEditingMeiosContatos="isEditing.meiosContatos"
                      :dentistas="dentistas"
                      :clinicas="clinicas"
                      :isSystemAdmin="$user.system_admin"
                      :novoContato="novoContato"
                      :getContatoPlaceholder="getContatoPlaceholder"
                      :hasPendingChanges="hasPendingChanges"
                      :possuiWhatsapp="possuiWhatsapp"
                      v-model:editModeActive="editModeActive"
                      @toggle-edit-pessoal="toggleEditPessoal"
                      @toggle-edit-responsavel="toggleEditResponsavel"
                      @toggle-edit-endereco="toggleEditEndereco"
                      @toggle-edit-mode="toggleEditMode"
                      @select-meio-contato="selectMeioContato"
                      @contato-change="contatoChange"
                      @adicionar-contato="adicionarContato"
                      @excluir-contato="excluirContato"
                      @get-endereco="getEndereco"
                      @update:field="updatePacienteField"
                      @save-changes="confirmSavePaciente"
                      @edit-mode-change="handleEditModeChange"
                      @handle-form-link-btn="handleFormLinkBtn"
                      @toggle-formulario-view="toggleFormularioView"
                      @iniciar-diagnostico-planejamento="iniciarDiagnosticoPlanejamento"
                    />
                  </div>

                  <!-- Desktop View (md and above) -->
                  <div class="d-none d-md-flex row g-0 mb-4">
                    <div class="col-md-6 border-end-md px-4">
                      <div class="section-header">
                        <div class="section-header-content">
                          <div class="section-icon">
                            <font-awesome-icon :icon="['fas', 'user']" />
                          </div>
                          <p class="text-uppercase mb-0">Informações pessoais</p>
                        </div>
                        <div class="section-actions">
                          <span
                            v-if="isFilledPessoal && !isEditingPessoal"
                            class="edit-icon-wrapper-light"
                            title="Editar informações pessoais"
                            @click="toggleEditPessoal">
                            <font-awesome-icon
                              :icon="['fas', 'edit']"
                              class="edit-icon-light"
                            />
                          </span>
                          <button
                            v-if="isEditingPessoal && isFilledPessoal"
                            class="btn-cancel-edit"
                            @click="cancelEditPessoal"
                          >
                            <i class="fas fa-times"></i>
                            Cancelar edição
                          </button>
                        </div>
                      </div>

                      <PatientPersonalInfo
                        :paciente="paciente"
                        :isEditing="isEditingPessoal"
                        :isMobile="false"
                        :dentistas="dentistas"
                        :clinicas="clinicas"
                        :isSystemAdmin="$user.system_admin"
                        :showResponsibleInfo="true"
                        @update:field="updatePacienteField"
                        @keyup="debouncedAutoSave"
                      />

                      <div class="p-horizontal-divider my-3"></div>
                      <div class="col-12">
                        <div class="section-header">
                          <div class="section-header-content">
                            <div class="section-icon">
                              <font-awesome-icon :icon="['fas', 'users']" />
                            </div>
                            <p class="text-uppercase mb-0">Informações do responsável</p>
                          </div>
                          <div class="section-actions">
                            <span
                              v-if="isFilledResponsavel && !isEditingResponsavel"
                              class="edit-icon-wrapper-light"
                              title="Editar informações do responsável"
                              @click="toggleEditResponsavel">
                              <font-awesome-icon
                                :icon="['fas', 'edit']"
                                class="edit-icon-light"
                              />
                            </span>
                            <button
                              v-if="isEditingResponsavel && isFilledResponsavel"
                              class="btn-cancel-edit"
                              @click="cancelEditResponsavel"
                            >
                              <i class="fas fa-times"></i>
                              Cancelar edição
                            </button>
                          </div>
                        </div>
                      </div>

                      <PatientResponsibleInfo
                        :paciente="paciente"
                        :isEditing="isEditingResponsavel"
                        :isMobile="false"
                        @update:field="updatePacienteField"
                        @keyup="debouncedAutoSave"
                      />

                      <div class="p-horizontal-divider"></div>
                      <div class="col-12">
                        <div class="section-header">
                          <div class="section-header-content">
                            <div class="section-icon">
                              <font-awesome-icon :icon="['fas', 'comment']" />
                            </div>
                            <p class="text-uppercase mb-0">Observações</p>
                          </div>
                        </div>
                        <textarea
                          class="form-control mt-3"
                          id="paciente_observacoes"
                          rows="3"
                          v-model="paciente.observacoes"
                        >
                        </textarea>
                      </div>
                    </div>

                    <div class="col-12 col-md-6 px-4">
                      <hr class="horizontal dark" />
                      <div class="section-header">
                        <div class="section-header-content">
                          <div class="section-icon">
                            <font-awesome-icon :icon="['fas', 'address-book']" />
                          </div>
                          <p class="text-uppercase mb-0">Meios de contato</p>
                        </div>
                        <div class="section-actions">
                          <span
                            v-if="!isEditing.meiosContatos && paciente?.contatos?.length > 0"
                            class="edit-icon-wrapper-light"
                            title="Gerenciar meios de contato"
                            @click="toggleEditMode('meiosContatos')">
                            <font-awesome-icon
                              :icon="['fas', 'edit']"
                              class="edit-icon-light"
                            />
                          </span>
                          <button
                            v-if="isEditing.meiosContatos"
                            class="btn-cancel-edit"
                            @click="cancelEditContatos"
                          >
                            <i class="fas fa-times"></i>
                            Cancelar edição
                          </button>
                        </div>
                      </div>

                      <PatientContactInfo
                        :paciente="paciente"
                        :isEditing="isEditing.meiosContatos"
                        :isMobile="false"
                        :novoContato="novoContato"
                        :novoContatoMask="novoContatoMask"
                        :getContatoPlaceholder="getContatoPlaceholder"
                        @select-meio-contato="selectMeioContato"
                        @contato-change="contatoChange"
                        @adicionar-contato="adicionarContato"
                        @excluir-contato="excluirContato"
                        @update:field="updatePacienteField"
                      />

                      <div class="p-horizontal-divider mb-0 w-100"></div>
                      <div class="section-header">
                        <div class="section-header-content">
                          <div class="section-icon">
                            <font-awesome-icon :icon="['fas', 'map-marker-alt']" />
                          </div>
                          <p class="text-uppercase mb-0">Endereço</p>
                        </div>
                        <div class="section-actions">
                          <span
                            v-if="isFilledEndereco && !isEditingEndereco"
                            class="edit-icon-wrapper-light"
                            title="Editar endereço"
                            @click="toggleEditEndereco">
                            <font-awesome-icon
                              :icon="['fas', 'edit']"
                              class="edit-icon-light"
                            />
                          </span>
                          <button
                            v-if="isEditingEndereco && isFilledEndereco"
                            class="btn-cancel-edit"
                            @click="cancelEditEndereco"
                          >
                            <i class="fas fa-times"></i>
                            Cancelar edição
                          </button>
                        </div>
                      </div>

                      <PatientAddressInfo
                        :paciente="paciente"
                        :isEditing="isEditingEndereco"
                        :isMobile="false"
                        @get-endereco="getEndereco"
                        @update:field="updatePacienteField"
                        @keyup="debouncedAutoSave"
                      />
                      <div class="p-horizontal-divider w-100"></div>
                      <div class="section-header ficha-header">
                        <div class="section-header-content">
                          <div class="section-icon">
                            <font-awesome-icon :icon="['fas', 'clipboard-check']" />
                          </div>
                          <p class="text-uppercase mb-0">Ficha de avaliação inicial</p>
                          <span v-if="paciente.formulario_respondido" class="badge badge-sm bg-success me-2">Respondida</span>
                          <span v-else class="badge badge-sm bg-warning me-2">Não respondida</span>
                        </div>
                        <div class="section-actions" v-if="!isLoading.paciente">
                          <button
                            v-if="!paciente.formulario_respondido"
                            class="btn btn-sm btn-primary mb-0 action-button"
                            @click="handleFormLinkBtn"
                          >
                            <font-awesome-icon
                              :icon="possuiWhatsapp ? ['fab', 'whatsapp'] : ['fas', 'copy']"
                              class="me-2"
                            />
                            <span>{{ possuiWhatsapp ? "ENVIAR LINK" : "COPIAR LINK" }}</span>
                          </button>
                          <div v-else class="d-flex align-items-center gap-2">
                            <button
                              class="btn btn-sm btn-outline-secondary mb-0 copy-link-button"
                              @click="copiarLink"
                              title="Copiar link da ficha de avaliação"
                            >
                              <font-awesome-icon :icon="['fas', 'copy']" />
                            </button>
                            <button
                              class="btn btn-sm btn-primary mb-0 action-button"
                              @click="toggleFormularioView"
                              data-bs-toggle="modal"
                              data-bs-target="#modalFormularioView"
                            >
                              <font-awesome-icon :icon="['fas', 'eye']" class="me-2" />
                              <span>VISUALIZAR</span>
                            </button>
                          </div>
                        </div>
                      </div>
                      <div class="p-horizontal-divider w-100"></div>
                      <div class="next-btn-container py-2 py-md-3 mt-3">
                        <button
                          class="btn btn-success mb-0"
                          @click="iniciarDiagnosticoPlanejamento"
                        >
                          <i class="me-2 fas fa-play" style="font-size: 13pt"></i>
                          <span class="uppercase" style="font-size: 10pt">
                            Iniciar diagnóstico e planejamento
                          </span>
                        </button>
                      </div>
                    </div>
                  </div>

                  <Transition name="fadeHeight">
                    <div v-if="hasPendingChanges && !isLoading.paciente" class="mt-4">
                      <div class="w-100 py-3 text-center">
                        <button
                          class="btn-save-changes"
                          @click="confirmSavePaciente"
                        >
                          <div class="btn-save-icon">
                            <i class="fas fa-save"></i>
                          </div>
                          <div class="btn-save-text">
                            <strong>Salvar alterações</strong>
                            <small>{{ changedFields.size }} {{ pluralizeChanges(changedFields.size) }}</small>
                          </div>
                        </button>
                      </div>
                    </div>
                  </Transition>

                  <div class="row mb-5 mt-4 mx-0">
                    <div class="col-12">
                      <div class="patient-details-container">
                        <div class="patient-details-header text-center mb-0">
                          <h5 class="mb-0">Detalhes do paciente</h5>
                        </div>

                        <div class="patient-details-content">
                          <div v-if="isLoading.paciente" class="w-100 text-center py-5">
                            <div class="spinner-border text-primary" role="status"></div>
                          </div>

                          <div v-if="!isLoading.paciente">
                            <div
                              v-if="
                                !paciente.formulario_respondido ||
                                detalhesPessoais.length == 0
                              "
                              class="empty-state-message"
                            >
                              <div class="icon-wrapper">
                                <font-awesome-icon
                                  :icon="['fas', 'clipboard-list']"
                                  class="empty-state-icon"
                                />
                              </div>
                              <p>
                                O paciente ainda não respondeu à ficha de avaliação inicial.
                                Para enviar-lhe o formulário, utilize o botão
                                "<font-awesome-icon
                                  :icon="
                                    possuiWhatsapp
                                      ? ['fab', 'fa-whatsapp']
                                      : ['fas', 'fa-copy']
                                  "
                                  class="me-1"
                                /><span class="font-weight-bold">{{
                                  possuiWhatsapp ? "ENVIAR LINK" : "COPIAR LINK"
                                }}</span
                                >" acima.
                              </p>
                            </div>
                            <div v-if="paciente.formulario_respondido" class="row g-3">
                              <div
                                v-for="(detalhe, index) in detalhesPessoais"
                                v-bind:key="index"
                                class="col-sm-6 col-md-4"
                              >
                                <div class="patient-info-item" :class="detalhe.nivel">
                                  <div class="info-icon">
                                    <font-awesome-icon
                                      :icon="['fas', getInfoIcon(detalhe.nivel)]"
                                    />
                                  </div>
                                  <div class="info-content">
                                    <span>{{ detalhe.detalhe }}</span>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>

                      <!-- Botão de exclusão centralizado -->
                      <div class="text-center">
                        <button
                          class="delete-patient-btn"
                          @click="confirmarExcluirPaciente"
                          title="Excluir paciente"
                        >
                          <font-awesome-icon :icon="['fas', 'trash-alt']" />
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </Transition>
              <Transition>
                <div v-if="activeProfileTab == 'perfilClinico'"></div>
              </Transition>
            </div>
            <!-- <div class="col-md-4">
          <profile-card />
        </div> -->
          </div>
        </div>
      </Transition>

      <Transition>
        <Planejamento
          v-if="activeTab == 'planejamento'"
          :paciente="paciente"
          @pacienteChange="refreshPaciente"
          @edit-mode-active="handleEditModeActive"
        />
      </Transition>

      <Transition>
        <Tratamento
          v-if="activeTab == 'tratamento'"
          :paciente="paciente"
          :dentistas="dentistas"
          @update:consultas="updateConsultas"
          @editar-consulta="editarConsulta"
          @ver-historico="verHistoricoConsulta"
          @pacienteChange="refreshPaciente"
        />
      </Transition>

      <Transition>
        <div class="py-4 container-fluid" v-if="activeTab == 'financeiro'">
          <paciente-financeiro
            :paciente="paciente"
            :faturas="faturasPaciente"
            :loading="loading.faturas"
            @refresh="loadFaturasPaciente"
            @create="openCreateFaturaModal"
            @create-orcamento="openCreateOrcamentoModal"
            @edit="openEditFaturaModal"
            @delete="deleteFaturaPaciente"
            @mark-paid="markFaturaAsPaid"
            @generate-receipt="generateReceipt"
          />

          <!-- Modal de Criação -->
          <financeiro-create-modal
            ref="financeiroCreateModal"
            @saved="onFinanceiroSaved"
          />
        </div>
      </Transition>
    </main>

    <div class="modal fade" tabindex="-1" id="modalFormularioView">
      <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">Ficha de avaliação inicial</h5>
            <button
              type="button"
              class="btn-close"
              data-bs-dismiss="modal"
              aria-label="Close"
            ></button>
          </div>
          <div class="modal-body p-3 px-4" style="max-height: 78vh; overflow-y: auto">
            <div class="d-flex flex-column align-center justify-content-center mb-0 pb-0">
              <label for=""><b>Respondida em:</b></label>
              <span>{{ $filters.dateTime(dataRespostaFicha) }}</span>
            </div>

            <div class="p-horizontal-divider"></div>

            <div style="max-width: 400px; margin: 0 auto">
              <div
                v-for="(question, index) in questoesFichaInicial"
                :key="index"
                class="mt-2 mb-4"
                :ref="'question' + index"
              >
                <label
                  v-if="
                    question.tipo !== 'text' &&
                    question.tipo !== 'date' &&
                    question.tipo !== 'phone' &&
                    question.tipo !== 'email'
                  "
                  class="mb-3 p-0 font-weight-bolder label-highlight"
                  >{{ question.questao }}
                  <span v-if="question.obrigatoria" class="text-danger">*</span>
                </label>

                <div
                  v-if="
                    question.tipo === 'text' ||
                    question.tipo === 'date' ||
                    question.tipo === 'phone' ||
                    question.tipo === 'email'
                  "
                  class="mt-0 p-0"
                >
                  <MaterialInput
                    :readonly="true"
                    :type="question.tipo === 'phone' ? 'text' : question.tipo"
                    :name="question.id"
                    :id="question.id"
                    :ref="question.id"
                    :label="question.questao"
                    labelClass="font-weight-bolder label-highlight"
                    v-model="question.resposta"
                    :required="question.obrigatoria"
                    :input="
                      function ($event) {
                        textInputEvent($event, question);
                      }
                    "
                    :placeholder="question.tipo === 'phone' ? '(##) #####-####' : null"
                    :style="
                      question.textOptions && question.textOptions.includes('center')
                        ? 'text-align: center !important'
                        : ''
                    "
                  />
                </div>

                <div v-else-if="question.tipo === 'checkbox'" class="px-3">
                  <table class="options-checkbox">
                    <tr
                      v-for="(alternativa, alternativaIndex) in question.alternativas"
                      :key="alternativaIndex"
                    >
                      <td class="d-flex flex-row align-center">
                        <input
                          type="checkbox"
                          class="form-checkbox"
                          :name="question.id + '-' + alternativa.resposta"
                          :id="question.id + '-' + alternativa.resposta"
                          :checked="alternativa.selecionada"
                          @click.prevent
                        />
                        <label
                          :for="question.id + '-' + alternativa.resposta"
                          style="padding-top: 5px"
                          >{{ alternativa.resposta }}</label
                        >
                      </td>
                    </tr>
                  </table>
                </div>

                <div v-else-if="question.tipo === 'radio'" class="row px-3">
                  <div
                    v-for="(alternativa, alternativaIndex) in question.alternativas"
                    v-bind:key="alternativaIndex"
                    class="col-6"
                    style="text-align: left"
                    :class="{
                      'ps-6': question.alternativas.length == 2 && alternativaIndex == 0,
                    }"
                  >
                    <input
                      type="radio"
                      class="form-radio"
                      :name="question.id"
                      :id="`alternativa-${question.id}-${alternativaIndex}`"
                      :value="alternativa.resposta"
                      v-model="question.resposta"
                      @click.prevent
                    />
                    <label
                      :for="`alternativa-${question.id}-${alternativaIndex}`"
                      class="radio-label"
                    >
                      {{ alternativa.resposta }}</label
                    >
                  </div>
                </div>

                <div
                  v-if="question.detalhar && question.detalhar === 'opcional'"
                  class="d-flex flex-row align-center justify-content-center"
                >
                  <input
                    type="checkbox"
                    class="form-checkbox"
                    :name="question.id + '-detalhar-cb'"
                    :id="question.id + '-detalhar-cb'"
                    v-model="question.detalhando"
                    @click.prevent
                  />
                  <label
                    :for="question.id + '-detalhar-cb'"
                    class="label-big"
                    style="padding-top: 8px"
                  >
                    {{
                      question.titulo_questao_detalhe
                        ? question.titulo_questao_detalhe
                        : "Detalhar..."
                    }}
                  </label>
                </div>

                <!-- Caso a questão tiver detalhamento obrigatório ou o detalhamento for optado pelo usuário -->
                <div v-if="question.detalhes">
                  <MaterialInput
                    :readonly="true"
                    :name="question.id + '-detalhar'"
                    label="Detalhes"
                    labelClass="label-big"
                    :id="question.id + '-detalhar'"
                    v-model="question.detalhes"
                  />
                </div>
                <!-- Exibe o divider, exceto no último elemento -->
                <div
                  v-if="index !== questoesFichaInicial.length - 1"
                  class="p-horizontal-divider primary"
                ></div>
              </div>
            </div>
            <!-- v-for / -->
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-default" data-bs-dismiss="modal">
              Fechar
            </button>
          </div>
        </div>
      </div>
    </div>
  <div v-if="isLoading.paciente" class="full-screen-spinner">
    <div class="spinner-border text-primary" role="status" style="width: 5rem; height: 5rem;"></div>
  </div>

  <!-- Botão de salvar removido - agora está abaixo dos formulários -->
</div>
</template>

<style>
/* button.lumi-fab {
  display: none !important;
} */
</style>

<style scoped>

.avatar {
  width: 80px !important;
  height: 80px !important;
}

.profile-pic {
  height: 80px;
  width: 80px;
  padding: 0px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  border-radius: 12px; /* rounded corners for the frame */
  box-shadow: 0 0 0 4px #f0f2f5; /* subtle beige almost white frame */
  transition: all 0.3s ease;
  overflow: hidden;
  cursor: pointer;
}

/* Patient info styles */
.patient-info {
  min-width: 0; /* Enable text truncation */
  max-width: calc(100% - 40px); /* Leave space for back button */
}

.id-ficha {
  font-size: 1.2rem;
  color: #6c757d;
  line-height: 1.1;
  margin-bottom: 2px;
  font-weight: 600;
}

.placeholder-text {
  opacity: 0.5;
}

.patient-name {
  line-height: 1.1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
  margin-bottom: 2px;
}

.long-name {
  font-size: 1.1rem !important; /* Smaller font for long names */
}

.patient-name {
  transition: all 0.2s ease;
  position: relative;
}

.patient-name:hover {
  color: #3a8bd6;
}

.patient-name .edit-icon-hover {
  transition: all 0.2s ease;
  opacity: 0;
  visibility: hidden;
  transform: translateX(-5px);
  color: inherit; /* Herda a cor do texto pai */
}

.patient-name:hover .edit-icon-hover {
  opacity: 1;
  visibility: visible;
  transform: translateX(0);
  color: #3a8bd6; /* Mesma cor do texto em hover */
}

/* Ajustes para telas mobile */
@media (max-width: 767.98px) {
  .id-ficha {
    font-size: 1.1rem !important;
  }

  .patient-name {
    font-size: 1.15rem !important;
  }

  .long-name {
    font-size: 1rem !important;
  }

  .patient-details-container {
    margin-top: 1.5rem !important;
  }
}

/* Back button styles */
.back-btn {
  flex-shrink: 0;
  margin-left: 15px;
  z-index: 5; /* Ensure button stays above other content */
  display: flex;
  align-items: center;
}

/* No need to hide desktop back button in edit mode */
/* The FAB button is hidden via v-if in the component */

.profile-pic::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(58, 139, 214, 0.4);
  border-radius: 8px;
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: 2;
  display: flex;
  align-items: center;
  justify-content: center;
}

.profile-pic:hover::before {
  opacity: 1;
}

.profile-pic::after {
  content: '\f030'; /* FontAwesome camera icon */
  font-family: 'Font Awesome 6 Free';
  font-weight: 900;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 1.5rem;
  opacity: 0;
  transition: all 0.3s ease;
  z-index: 3;
  pointer-events: none;
}

.profile-pic:hover::after {
  opacity: 1;
  transform: translate(-50%, -50%) scale(1.1);
}

/* Tooltip para indicar ação */

.profile-pic:hover {
  box-shadow: 0 0 0 4px #3a8bd6, 0 8px 25px rgba(58, 139, 214, 0.3); /* blue frame + shadow on hover */
  transform: scale(1.02);
}

.profile-pic:active {
  transform: scale(0.98);
  transition: transform 0.1s ease;
}

.profile-pic img {
  height: 100%;
  width: 100%;
  object-fit: cover;
  border-radius: 8px; /* slightly smaller radius to show frame */
  border: 2px solid white; /* white border between image and frame */
  position: relative;
  z-index: 1;
  transition: transform 0.3s ease;
}

.full-screen-spinner {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: rgba(255, 255, 255, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1050; /* above most elements */
}

/* Patient Details Section Styles */
.patient-details-container {
  border-top: 1px solid #eee;
  background: linear-gradient(to bottom, #f8fbff, #ffffff);
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.03);
  overflow: hidden;
  transition: all 0.3s ease;
  margin-left: 0.75rem;
  margin-right: 0.75rem;
}

.patient-details-header {
  background: linear-gradient(135deg, #3a8bd6, #5a9bd5);
  padding: 1rem;
  position: relative;
  overflow: hidden;
  border-radius: 12px 12px 0 0;
}

.patient-details-header h5 {
  color: white;
  font-weight: 500;
  font-size: 1.25rem;
  position: relative;
  z-index: 2;
  letter-spacing: 0.5px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.patient-details-header::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
  z-index: 1;
}

.patient-details-content {
  padding: 1.5rem;
  background-color: white;
  border-radius: 0 0 12px 12px;
}

/* Empty state styling */
.empty-state-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem 1.5rem;
  text-align: center;
  background-color: #f8fafc;
  border-radius: 8px;
  margin: 0.5rem 0;
  border: 1px dashed #d1dce8;
}

.empty-state-message .icon-wrapper {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: linear-gradient(135deg, #e6f2ff, #d1e6ff);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1rem;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
}

.empty-state-message .empty-state-icon {
  font-size: 1.5rem;
  color: #5a9bd5;
}

.empty-state-message p {
  color: #5c6b7a;
  font-size: 1rem;
  line-height: 1.5;
  max-width: 500px;
  margin: 0 auto;
}

/* Patient info items styling */
.patient-info-item {
  display: flex;
  align-items: flex-start;
  padding: 12px 15px;
  border-radius: 8px;
  background-color: #f9fafc;
  border-left: 4px solid #ddd;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.04);
  transition: all 0.2s ease;
  height: 100%;
}

.patient-info-item:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.08);
  transform: translateY(-2px);
}

.patient-info-item.positivo {
  border-left-color: #4CAF50;
  background-color: #f1f8f1;
}

.patient-info-item.neutro {
  border-left-color: #80A1BB;
  background-color: #f5f9fc;
}

.patient-info-item.atencao {
  border-left-color: #FFC107;
  background-color: #fffbf0;
}

.patient-info-item.negativo {
  border-left-color: #F44336;
  background-color: #fef5f4;
}

.patient-info-item .info-icon {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  margin-right: 12px;
  background-color: rgba(255, 255, 255, 0.8);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  flex-shrink: 0;
}

.patient-info-item.positivo .info-icon {
  color: #4CAF50;
}

.patient-info-item.neutro .info-icon {
  color: #80A1BB;
}

.patient-info-item.atencao .info-icon {
  color: #FFC107;
}

/* Estilos para o botão de exclusão de paciente */
.delete-patient-btn {
  width: 42px;
  height: 42px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #dc3545;
  border: 1px solid #dc3545;
  transition: all 0.3s ease;
  box-shadow: 0 2px 10px rgba(220, 53, 69, 0.3);
  padding: 0;
  margin: 1.5rem auto 0;
  font-size: 1rem;
}

.delete-patient-btn:hover {
  background-color: #dc3545;
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(220, 53, 69, 0.4);
}

.patient-info-item.negativo .info-icon {
  color: #F44336;
}

.patient-info-item .info-content {
  flex-grow: 1;
  font-size: 0.95rem;
  line-height: 1.4;
  padding-top: 2px;
}

/* Section Headers Styling */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 5px 6px;
  margin: 15px 0 10px;
  border-radius: 8px;
  background: linear-gradient(to right, #f8f9fa, #f1f3f5);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  border-left: 4px solid #5a9bd5;
  transition: all 0.2s ease;
  flex-wrap: nowrap;
  flex-direction: row;
}

.section-header:hover {
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.08);
  transform: translateY(-1px);
}

.section-header-content {
  display: flex;
  align-items: center;
  max-width: calc(100% - 80px);
  overflow: hidden;
  flex-shrink: 1;
  flex-direction: row;
  flex-wrap: wrap;
}

.section-icon {
  width: 26px;
  height: 26px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  margin-right: 0;
  background-color: rgba(90, 155, 213, 0.1);
  color: #5a9bd5;
  flex-shrink: 0;
}

.section-icon svg {
  font-size: 14pt;
}

.section-header p {
  font-weight: 600;
  font-size: 0.8rem;
  color: #495057;
  letter-spacing: 0.3px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-left: 8px;
  display: inline-flex;
  align-items: center;
}

.section-header-content .badge {
  margin-left: 8px;
  align-self: center;
}

.section-header-content span {
  display: inline-flex;
  align-items: center;
}

.section-actions {
  display: flex;
  align-items: center;
  flex-shrink: 0;
  min-width: 70px;
  justify-content: center;
}

.section-actions .badge {
  padding: 0.4em 0.6em;
  font-size: 0.7rem;
  font-weight: 500;
  letter-spacing: 0.3px;
  border-radius: 4px;
  text-transform: uppercase;
}

.section-actions .action-button {
  font-size: 0.7rem;
  font-weight: 500;
  letter-spacing: 0.3px;
  text-transform: uppercase;
  padding: 0.6em 1.4em;
  border-radius: 6px;
  white-space: nowrap;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
}

.section-actions .action-button svg {
  font-size: 1.7em;
  display: flex;
  align-items: center;
  justify-content: center;
}

.section-actions .action-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);
}

.copy-link-button {
  width: 40px !important;
  height: 32px !important;
  padding: 0 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  border: 1px solid #dee2e6 !important;
  background-color: #f8f9fa !important;
  color: #6c757d !important;
  transition: all 0.2s ease !important;
}

.copy-link-button:hover {
  background-color: #e9ecef !important;
  color: #495057 !important;
  border-color: #adb5bd !important;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.ficha-header {
  width: 100%;
  margin-top: 0;
}

.cancel-edit-text {
  font-size: 9pt;
}

.pointer {
  cursor: pointer;
}

/* Estilos para o ícone de edição em fundo branco */
.edit-icon-wrapper-light {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.05);
  margin-left: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.edit-icon-wrapper-light:hover {
  background-color: rgba(0, 0, 0, 0.1);
  transform: scale(1.1);
  box-shadow: 0 0 0 2px rgba(0, 0, 0, 0.03);
}

.edit-icon-light {
  color: #495057;
  font-size: 14px;
}

/* Navbar horizontal em todas as telas */
.menu-2x2 {
  display: flex !important;
  flex-direction: row !important;
  flex-wrap: nowrap !important;
  justify-content: space-between !important;
  width: 100%;
}

.menu-2x2 .nav-item {
  flex: 1;
  text-align: center;
}

.nav-tab {
  border-radius: 8px;
  transition: all 0.3s ease;
}

/* Ajustes para telas pequenas */
@media (max-width: 767.98px) {
  .menu-2x2 {
    background-color: #f8f9fa;
    border-radius: 10px;
    padding: 0.25rem !important;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
  }

  .menu-2x2 .nav-item {
    margin: 0 3px;
  }

  .menu-2x2 .nav-item .nav-link {
    padding: 0.5rem 0.25rem !important;
    font-size: 0.85rem;
    min-height: 60px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    background-color: white;
  }

  .menu-2x2 .nav-item .nav-link.active {
    transform: translateY(-2px);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  }

  .menu-2x2 .nav-item .nav-link i {
    font-size: 1.1rem;
    margin-bottom: 0.25rem;
  }

  .menu-2x2 .nav-item .nav-link span {
    font-size: 0.7rem;
    font-weight: 500;
  }

  /* Mobile accordion styles */
  .patient-accordion .accordion-button {
    padding: 0.75rem 1rem;
  }

  .patient-accordion .accordion-body {
    padding: 1rem;
  }

  .patient-accordion .accordion-item {
    margin-bottom: 0.75rem;
  }
}

/* Desktop layout fixes */
@media (min-width: 768px) {
  .row.g-0 {
    display: flex;
    flex-wrap: wrap;
  }

  .border-end-md {
    border-right: 1px solid #dee2e6;
  }
}

/* Modal fade animation */
.modal.fade {
  transition: opacity 0.3s ease-in-out;
}

.modal.fade .modal-dialog {
  transition: transform 0.3s ease-out;
  transform: translate(0, -25px);
}

.modal.show .modal-dialog {
  transform: translate(0, 0);
}

.modal-backdrop.fade {
  opacity: 0;
  transition: opacity 0.15s linear;
}

.modal-backdrop.show {
  opacity: 0.5;
}

/* ===== ESTILOS DO SISTEMA DE AUTO-SAVE ===== */

/* Toast de auto-save discreto */
.autosave-toast {
  animation: slideInRight 0.3s ease-out;
}

.autosave-toast .toast-header {
  padding: 0.5rem 0.75rem;
  font-size: 0.875rem;
}

.autosave-toast .toast-body {
  padding: 0.5rem 0.75rem;
  font-size: 0.875rem;
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* FAB de alterações não salvas */
.unsaved-changes-fab {
  position: fixed;
  bottom: 30px;
  right: 30px;
  z-index: 1060;
  opacity: 0;
  transform: translateY(100px) scale(0.8);
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  pointer-events: none;
}

.unsaved-changes-fab.show {
  opacity: 1;
  transform: translateY(0) scale(1);
  pointer-events: auto;
}

.unsaved-changes-fab .fab-content {
  background: linear-gradient(135deg, #ff6b6b, #ee5a52);
  color: white;
  padding: 1rem 1.25rem;
  border-radius: 50px;
  box-shadow: 0 8px 25px rgba(255, 107, 107, 0.4);
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  min-width: 280px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.unsaved-changes-fab .fab-content:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 35px rgba(255, 107, 107, 0.5);
}

.unsaved-changes-fab .fab-content::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.unsaved-changes-fab .fab-content:hover::before {
  left: 100%;
}

.unsaved-changes-fab .fab-icon {
  font-size: 1.25rem;
  animation: pulse 2s infinite;
}

.unsaved-changes-fab .fab-text strong {
  display: block;
  font-size: 0.95rem;
  margin-bottom: 0.125rem;
}

.unsaved-changes-fab .fab-text small {
  font-size: 0.8rem;
  opacity: 0.9;
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

/* Tooltip do FAB */
.unsaved-changes-fab .fab-tooltip {
  position: absolute;
  bottom: 100%;
  right: 0;
  margin-bottom: 1rem;
  opacity: 0;
  visibility: hidden;
  transform: translateY(10px);
  transition: all 0.3s ease;
  pointer-events: none;
}

.unsaved-changes-fab:hover .fab-tooltip {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
  pointer-events: auto;
}

.unsaved-changes-fab .tooltip-content {
  background: white;
  border-radius: 12px;
  padding: 1.25rem;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
  border: 1px solid #e9ecef;
  min-width: 320px;
  max-width: 400px;
}

.unsaved-changes-fab .tooltip-content h6 {
  color: #495057;
  margin-bottom: 0.75rem;
  font-weight: 600;
}

.unsaved-changes-fab .tooltip-content p {
  color: #6c757d;
  margin-bottom: 1rem;
  line-height: 1.4;
}

.unsaved-changes-fab .tooltip-actions {
  display: flex;
  gap: 0.5rem;
}

.unsaved-changes-fab .tooltip-actions .btn {
  font-size: 0.875rem;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-weight: 500;
}

/* Campo alterado highlight */
.field-changed {
  border-color: #007bff !important;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25) !important;
  animation: fieldHighlight 0.6s ease-out;
}

/* Campo com alteração pendente */
.field-pending {
  border-color: #28a745 !important;
  border-width: 2px !important;
  box-shadow: 0 0 0 0.1rem rgba(40, 167, 69, 0.2) !important;
  font-weight: 600 !important;
  color: #155724 !important;
}

.field-pending::placeholder {
  color: #6c757d !important;
  font-weight: normal !important;
}

/* Para inputs do tipo select */
.field-pending option {
  font-weight: normal !important;
  color: #212529 !important;
}

@keyframes fieldHighlight {
  0% {
    background-color: rgba(0, 123, 255, 0.1);
  }
  100% {
    background-color: transparent;
  }
}

/* Campo restaurado highlight */
.field-restored {
  border-color: #28a745 !important;
  box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25) !important;
  background-color: rgba(40, 167, 69, 0.05) !important;
  transition: all 0.3s ease;
}

.field-restored-pulse {
  animation: fieldRestored 2s ease-in-out;
}

@keyframes fieldRestored {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
  }
  50% {
    transform: scale(1.02);
    box-shadow: 0 0 0 0.3rem rgba(40, 167, 69, 0.4);
  }
}

/* Responsividade para mobile */
@media (max-width: 768px) {
  .unsaved-changes-fab {
    bottom: 20px;
    right: 20px;
  }

  .unsaved-changes-fab .fab-content {
    min-width: 260px;
    padding: 0.875rem 1rem;
  }

  .unsaved-changes-fab .tooltip-content {
    min-width: 280px;
    max-width: calc(100vw - 40px);
  }

  .unsaved-changes-fab .tooltip-actions {
    flex-direction: column;
  }

  .unsaved-changes-fab .tooltip-actions .btn {
    width: 100%;
  }
}

/* ===== ESTILOS DO MODAL DE RECUPERAÇÃO DE RASCUNHO ===== */

/* Modal de recuperação */
:deep(.draft-recovery-popup) {
  border-radius: 16px !important;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15) !important;
}

.draft-recovery-modal {
  text-align: left;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.recovery-header {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #e9ecef;
}

.recovery-icon {
  flex-shrink: 0;
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #17a2b8, #20c997);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.5rem;
}

.recovery-message {
  margin: 0;
  font-size: 1rem;
  line-height: 1.5;
  color: #495057;
}

/* Preview das alterações */
.changes-preview {
  margin-bottom: 1.5rem;
}

.changes-title {
  color: #495057;
  font-weight: 600;
  margin-bottom: 1rem;
  font-size: 0.95rem;
}

.changes-table-container {
  max-height: 350px;
  overflow-y: auto;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  background: white;
}

/* Tabela de alterações */
.changes-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.9rem;
}

.changes-table thead th {
  background: linear-gradient(135deg, #495057, #6c757d);
  color: white;
  padding: 0.75rem;
  text-align: left;
  font-weight: 600;
  font-size: 0.85rem;
  border-bottom: 2px solid #dee2e6;
  position: sticky;
  top: 0;
  z-index: 10;
}

.changes-table thead th:first-child {
  border-top-left-radius: 8px;
}

.changes-table thead th:last-child {
  border-top-right-radius: 8px;
}

/* Linha de categoria */
.changes-table .category-row td {
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  padding: 0.6rem 0.75rem;
  font-weight: 600;
  color: #495057;
  border-bottom: 1px solid #dee2e6;
  font-size: 0.9rem;
}

/* Linhas de alteração */
.changes-table .change-row {
  transition: background-color 0.2s ease;
}

.changes-table .change-row:hover {
  background-color: #f8f9fa;
}

.changes-table .change-row td {
  padding: 0.6rem 0.75rem;
  border-bottom: 1px solid #f1f3f4;
  vertical-align: top;
}

.changes-table .field-name {
  font-weight: 500;
  color: #495057;
  min-width: 140px;
}

.changes-table .old-value {
  background: #fff3cd;
  color: #856404;
  padding: 0.3rem 0.5rem;
  border-radius: 4px;
  border: 1px solid #ffeaa7;
  font-family: 'Courier New', monospace;
  font-size: 0.85rem;
  display: inline-block;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.changes-table .new-value {
  background: #d1edff;
  color: #0c5460;
  padding: 0.3rem 0.5rem;
  border-radius: 4px;
  border: 1px solid #bee5eb;
  font-family: 'Courier New', monospace;
  font-weight: 600;
  font-size: 0.85rem;
  display: inline-block;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.changes-table .old-value em,
.changes-table .new-value em {
  font-style: italic;
  opacity: 0.7;
}

/* Footer do modal */
.recovery-footer {
  padding-top: 1rem;
  border-top: 1px solid #e9ecef;
  text-align: center;
}

.timestamp {
  color: #6c757d;
  font-size: 0.85rem;
  margin-bottom: 1rem;
}

.recovery-question {
  margin: 0;
  font-size: 1.05rem;
  color: #495057;
}

/* Botões customizados */
:deep(.btn-recovery-confirm) {
  background: linear-gradient(135deg, #28a745, #20c997) !important;
  border: none !important;
  border-radius: 8px !important;
  padding: 0.75rem 1.5rem !important;
  font-weight: 600 !important;
  transition: all 0.3s ease !important;
}

:deep(.btn-recovery-confirm:hover) {
  transform: translateY(-1px) !important;
  box-shadow: 0 8px 25px rgba(40, 167, 69, 0.3) !important;
}

:deep(.btn-recovery-cancel) {
  background: linear-gradient(135deg, #dc3545, #c82333) !important;
  border: none !important;
  border-radius: 8px !important;
  padding: 0.75rem 1.5rem !important;
  font-weight: 600 !important;
  transition: all 0.3s ease !important;
}

:deep(.btn-recovery-cancel:hover) {
  transform: translateY(-1px) !important;
  box-shadow: 0 8px 25px rgba(220, 53, 69, 0.3) !important;
}

/* Modal de sucesso */
:deep(.success-recovery-popup) {
  border-radius: 16px !important;
}

.success-icon {
  animation: successPulse 0.6s ease-out;
}

@keyframes successPulse {
  0% {
    transform: scale(0.8);
    opacity: 0;
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Scrollbar customizada para tabela de alterações */
.changes-table-container::-webkit-scrollbar {
  width: 6px;
}

.changes-table-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.changes-table-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.changes-table-container::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Botão de salvar alterações */
.btn-save-changes {
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
  color: white;
  border: none;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 123, 255, 0.3);
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: inline-flex;
  align-items: center;
  padding: 12px 20px;
  min-width: 220px;
}

.btn-save-changes:hover {
  transform: translateY(-2px) scale(1.02);
  box-shadow: 0 6px 25px rgba(0, 123, 255, 0.4);
  background: linear-gradient(135deg, #0056b3 0%, #004085 100%);
}

.btn-save-changes .btn-save-icon {
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  font-size: 16px;
}

.btn-save-changes .btn-save-text {
  flex: 1;
  line-height: 1.3;
  text-align: left;
}

.btn-save-changes .btn-save-text strong {
  display: block;
  font-size: 14px;
  margin-bottom: 2px;
  font-weight: 600;
}

.btn-save-changes .btn-save-text small {
  font-size: 12px;
  opacity: 0.9;
}

/* Responsivo */
@media (max-width: 768px) {
  .btn-save-changes {
    min-width: 200px;
    padding: 10px 16px;
  }
}

/* Botões de cancelar edição */
.btn-cancel-edit {
  background: linear-gradient(135deg, #dc3545, #c82333);
  color: white;
  border: none;
  border-radius: 6px;
  padding: 5px 10px;
  font-size: 11px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  gap: 5px;
  margin-left: 8px;
  line-height: 1;
}

.btn-cancel-edit:hover {
  background: linear-gradient(135deg, #c82333, #a71e2a);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(220, 53, 69, 0.3);
}

.btn-cancel-edit i {
  font-size: 9px;
  line-height: 1;
}

.btn-cancel-edit:active {
  transform: translateY(0);
}

</style>

<script>
import { capitalizeAll, phoneMask } from "@/helpers/utils.js";
import setNavPills from "@/assets/js/nav-pills.js";
import setTooltip from "@/assets/js/tooltip.js";
// import ProfileCard from "./components/ProfileCard.vue";
import MaterialInput from "@/components/MaterialInput.vue";
import MaterialButton from "@/components/MaterialButton.vue";
import PatientAccordion from "@/components/PatientAccordion.vue";
import PatientPersonalInfo from "@/components/PatientPersonalInfo.vue";
import PatientResponsibleInfo from "@/components/PatientResponsibleInfo.vue";
import PatientContactInfo from "@/components/PatientContactInfo.vue";
import PatientAddressInfo from "@/components/PatientAddressInfo.vue";
import BackButton from "@/components/BackButton.vue";
import Planejamento from "@/views/Planejamento.vue";
import Tratamento from "@/views/Tratamento.vue";
import PacienteFinanceiro from "@/components/Financeiro/PacienteFinanceiro.vue";
import FinanceiroCreateModal from "@/components/Financeiro/FinanceiroCreateModal.vue";
import { getEnderecoByCep } from "@/services/commonService";
import { uploadImage } from "@/services/imagensService";
import { getClinicaDentistas } from "@/services/dentistasService";
import { getGlobalDraftsInstance } from "@/composables/useGlobalDrafts";
import { financeiroService } from "@/services/financeiroService";
import {
  getPaciente,
  updatePaciente,
  adicionarMeioContato,
  excluirMeioContato,
  getFichaInicial,
  excluirPaciente,
} from "@/services/pacientesService";
import { getClinicas } from "@/services/clinicasService";
import cSwal from "@/utils/cSwal.js";
import { showAutoSaveToast, showInfoToast } from "@/utils/toastHelper.js";

var isEditing = [];

var paciente = {
  clinica: {
    id: null,
  },
};
var originalPaciente = {};

var showTratamento = false;

var activeProfileTab = "perfilPessoal";

var activeTab = "perfil";

let clinicaSlug
let pacienteIdFicha

export default {
  name: "profile",
  components: {
    // ProfileCard,
    MaterialInput,
    MaterialButton,
    Planejamento,
    Tratamento,
    PatientAccordion,
    PatientPersonalInfo,
    PatientResponsibleInfo,
    PatientContactInfo,
    PatientAddressInfo,
    BackButton,
    PacienteFinanceiro,
    FinanceiroCreateModal,
  },
  data() {
    return {
      clinicas: [],
      dataRespostaFicha: null,
      questoesFichaInicial: [],
      isLoading: {
        paciente: true,
        profilePicture: true,
      },
      loading: {
        faturas: false,
      },
      faturasPaciente: [],
      dentistas: [],
      isEditing,
      novoContato: {
        tipo: "whatsapp",
        contato: "",
        descricao: "",
      },
      novoContatoMask: "",
      showMenu: false,
      paciente,
      originalPaciente,
      showTratamento,
      activeTab,
      activeProfileTab,
      // New states for edit mode of blocks
      isEditingPessoal: false,
      isEditingResponsavel: false,
      isEditingEndereco: false,
      // New states to track if blocks are filled (readonly)
      isFilledPessoal: false,
      isFilledResponsavel: false,
      isFilledEndereco: false,
      // State to track if any accordion is in edit mode
      isEditMode: false,
      // State to track if edit mode is active (for hiding the back button)
      editModeActive: false,
      // Auto-save system
      autoSaveTimer: null,
      autoSaveDelay: 3000, // 3 segundos
      isAutoSaving: false,
      lastAutoSaveTime: null,
      // Timer para toast de rascunho salvo
      toastTimer: null,
      // Polling timer para fallback
      pollingTimer: null,
      // Flag para indicar quando estamos restaurando dados (não deve acionar auto-save)
      isRestoringFromCache: false,
      // Cache system
      cacheKey: null,
      // Changed fields tracking
      changedFields: new Set(),
      // Global drafts instance
      globalDrafts: getGlobalDraftsInstance(),
    };
  },
  computed: {
    isAnyEditing() {
      return this.isEditingPessoal ||
             this.isEditingResponsavel ||
             this.isEditingEndereco ||
             (this.isEditing && this.isEditing.meiosContatos);
    },
    getContatoPlaceholder() {
      var placeholder = null;
      switch (this.novoContato.tipo) {
        case "whatsapp":
          placeholder = "WhatsApp";
          break;
        case "celular":
          placeholder = "Celular";
          break;
        case "telefone":
          placeholder = "Telefone";
          break;
        case "email":
          placeholder = "E-mail";
          break;
      }

      return placeholder;
    },
    possuiWhatsapp() {
      return (
        this.paciente &&
        this.paciente.contatos &&
        this.paciente.contatos.some((contato) => contato.tipo === "whatsapp")
      );
    },
    whatsappNumero() {
      if (this.possuiWhatsapp) {
        const whatsappContato = this.paciente.contatos.find(
          (contato) => contato.tipo === "whatsapp"
        );
        return whatsappContato.contato;
      } else {
        return null;
      }
    },
    detalhesPessoais() {
      return this.paciente.detalhes_paciente
        ? this.paciente.detalhes_paciente.filter((detalhe) => detalhe.tipo == "pessoal")
        : [];
    },
    hasPendingChanges() {
      if (!this.originalPaciente || !this.paciente) return false;

      // Função para comparar objetos de forma mais confiável
      const compareObjects = (obj1, obj2) => {
        // Comparar propriedades simples
        const simpleProps = ['nome', 'cpf', 'rg', 'data_nascimento', 'como_conheceu',
                            'nome_mae', 'nome_pai', 'observacoes', 'dentista_id',
                            'responsavel_nome', 'responsavel_cpf', 'responsavel_rg',
                            'endereco_cep', 'endereco_estado', 'endereco_cidade',
                            'endereco_logradouro', 'endereco_numero', 'endereco_complemento'];

        for (const prop of simpleProps) {
          if (obj1[prop] !== obj2[prop]) {
            return true; // Encontrou uma diferença
          }
        }

        // Comparar clinica.id se existir
        if (obj1.clinica && obj2.clinica && obj1.clinica.id !== obj2.clinica.id) {
          return true;
        }

        return false; // Nenhuma diferença encontrada
      };

      return compareObjects(this.paciente, this.originalPaciente);
    },
    // Computed property para verificar se há mudanças não salvas (incluindo auto-save)
    hasUnsavedChanges() {
      return this.hasPendingChanges && !this.isAutoSaving;
    },
    // Cache key único para este paciente
    patientCacheKey() {
      return `patient_cache_${this.paciente?.id || this.$route.params.id_ficha}`;
    },
  },
  watch: {
    paciente: {
      handler() {
        // Normalizar strings vazias para null
        for (const propriedade in this.paciente)
          if (this.paciente[propriedade] === "") this.paciente[propriedade] = null;

        // Detectar mudanças para auto-save (apenas se não estamos carregando dados ou restaurando cache)
        if (!this.isLoading.paciente && !this.isRestoringFromCache && this.originalPaciente && Object.keys(this.originalPaciente).length > 0) {
          this.detectChangesAndScheduleAutoSave();
        }
      },
      deep: true, // Observação profunda de alterações aninhadas
    },
    // Watcher para salvar no cache local quando há mudanças
    hasPendingChanges: {
      handler(hasChanges) {
        if (hasChanges && !this.isRestoringFromCache) {
          this.saveToLocalCache();
        }
        // Não limpar cache automaticamente - só quando salvar no banco ou descartar
      },
      immediate: false
    },
    // Watcher para detectar mudança de aba
    activeTab: {
      handler(newTab, oldTab) {
        if (oldTab === 'perfil' && newTab !== 'perfil' && this.hasPendingChanges) {
          // Se saindo da aba perfil com mudanças pendentes, forçar auto-save
          this.performAutoSave();
        }

        // Carregar faturas quando a aba financeiro for ativada
        if (newTab === 'financeiro' && this.paciente.id) {
          this.loadFaturasPaciente();
        }
      }
    },
    // Watcher para aplicar estilos de campos pendentes
    changedFields: {
      handler() {
        this.$nextTick(() => {
          setTimeout(() => {
            this.applyPendingFieldStyles();
          }, 100);
        });
      },
      deep: true
    }
  },
  methods: {
    async refreshDentistas() {
      if (this.$clinica)
        this.dentistas = await getClinicaDentistas(this.$clinica.id);
    },
    async refreshClinicas() {
      if (!this.$user.system_admin)
        return
      this.clinicas = await getClinicas();
    },
    capitalizeAll,
    phoneMask,
    iniciarDiagnosticoPlanejamento() {
      this.openTab("planejamento");
    },

    updateConsultas(consultas) {
      if (consultas && Array.isArray(consultas)) {
        this.paciente.consultas = consultas;
      }
    },

    editarConsulta(id) {
      // Acessar o componente ConsultaModal através da referência e chamar o método
      this.$refs.consultaModal.abrirModalEditarConsulta(id);
    },

    verHistoricoConsulta(id) {
      // Acessar o componente HistoricoConsultaModal através da referência e chamar o método
      this.$refs.historicoModal.abrirModal(id);
    },

    recarregarConsultas() {
      // Recarregar o paciente inteiro para atualizar as consultas
      this.refreshPaciente();
    },
    profilePictureLoaded() {
      this.isLoading.profilePicture = false;
      this.isLoading.paciente = false; // Also hide the full screen spinner when profile picture is loaded
    },
    toggleFormularioView() {
      // Placeholder for toggling the form view modal if needed
    },

    getFichaInicialLink() {
      return `${window.location.origin}/bem-vindo/?t=${this.paciente.public_token}`;
    },

    contatoChange(event) {
      // Update the parent's novoContato object to stay in sync with the child component
      if (event && event.target) {
        this.novoContato.contato = event.target.value;
      }
    },

    cancelPhotoUpload() {
      this.pendingPhotoFile = null;
      this.photoPreviewImage = null;
    },

    confirmChangeName() {
      cSwal.cConfirm("Deseja alterar o nome do paciente?", () => {
        this.changeName();
      });
    },

    changeName() {
      cSwal.fire({
        title: 'Alterar nome do paciente',
        input: 'text',
        inputValue: this.paciente.nome,
        inputLabel: 'Novo nome:',
        inputPlaceholder: 'Digite o novo nome',
        showCancelButton: true,
        confirmButtonText: 'Salvar',
        cancelButtonText: 'Cancelar',
        customClass: {
          input: 'text-center',
          popup: 'swal-wider-popup'
        },
        inputValidator: (value) => {
          if (!value || value.trim() === '') {
            return 'O nome não pode estar vazio';
          }
        },
        didOpen: () => {
          // Adicionar estilo para centralizar o texto do input
          const style = document.createElement('style');
          style.innerHTML = `
            .swal2-input {
              text-align: center !important;
              font-size: 1.1em !important;
            }
            .swal-wider-popup {
              width: 32em !important;
            }
          `;
          document.head.appendChild(style);
        }
      }).then(async (result) => {
        if (result.isConfirmed) {
          const newName = result.value.trim();
          if (newName !== this.paciente.nome) {
            cSwal.loading("Atualizando nome do paciente...");

            // Aplicar capitalização ao nome (primeira letra de cada palavra em maiúscula)
            const capitalizedName = this.capitalizeAllStr(newName);

            // Criar uma cópia do paciente para atualização
            const pacienteToUpdate = { ...this.paciente };
            pacienteToUpdate.nome = capitalizedName;

            const update = await updatePaciente(pacienteToUpdate);

            if (update) {
              // Atualizar o nome localmente com a versão capitalizada
              this.paciente.nome = capitalizedName;
              this.originalPaciente.nome = capitalizedName;

              cSwal.loaded();
              cSwal.cSuccess("Nome do paciente atualizado com sucesso.");
            } else {
              cSwal.loaded();
              cSwal.cError("Ocorreu um erro ao atualizar o nome do paciente.");
            }
          }
        }
      });
    },

    confirmUpdatePhoto() {
      cSwal.cConfirm("Deseja atualizar a foto de perfil?", () => {
        this.chooseProfilePictureFile();
      });
    },

    chooseProfilePictureFile() {
      document.getElementById("profilePictureFileInput").value = "";
      document.getElementById("profilePictureFileInput").click();
    },

    profilePicUpload(e) {
      cSwal.loading("Atualizando imagem de perfil...");

      const imagem = e.target.files[0];
      const reader = new FileReader();
      reader.readAsDataURL(imagem);
      reader.onload = async () => {
        const imgData = {
          paciente_id: this.paciente.id,
          imagem,
          dir: "profile_pic",
        };
        const upload = await uploadImage(imgData);

        if (upload) {
          await this.refreshPaciente();
          cSwal.loaded();
          cSwal.cSuccess("A foto de perfil do paciente foi atualizada.");
        } else {
          cSwal.loaded();
          cSwal.cError("Ocorreu um erro ao atualizar a foto de perfil do paciente.");
        }
      };
    },

    capitalizeAllStr(str) {
      return str.replace(/\b\w/g, (l) => l.toUpperCase());
    },

    normalizeEmptyStrings(obj) {
      // Função para normalizar strings vazias para null em um objeto
      if (obj === null || obj === undefined) return obj;

      if (typeof obj === 'string' && obj === '') {
        return null;
      }

      if (Array.isArray(obj)) {
        return obj.map(item => this.normalizeEmptyStrings(item));
      }

      if (typeof obj === 'object') {
        const normalized = {};
        for (const key in obj) {
          if (obj.hasOwnProperty(key)) {
            normalized[key] = this.normalizeEmptyStrings(obj[key]);
          }
        }
        return normalized;
      }

      return obj;
    },

    toggleEditMode(section) {
      this.isEditing[section] = !this.isEditing[section];
      this.isEditMode = this.isEditing[section];
    },

    handleEditModeChange(isEditing) {
      console.log('handleEditModeChange chamado com:', isEditing);
      this.isEditMode = isEditing;

      // Forçar a atualização da propriedade computada isAnyEditing
      if (isEditing) {
        // Se estamos entrando no modo de edição, verificamos qual accordion está aberto
        // e atualizamos o estado correspondente
        const accordions = document.querySelectorAll('.accordion-collapse.show');
        accordions.forEach(accordion => {
          const id = accordion.id;
          if (id === 'collapsePersonalInfo') {
            this.isEditingPessoal = true;
          } else if (id === 'collapseResponsibleInfo') {
            this.isEditingResponsavel = true;
          } else if (id === 'collapseContactInfo') {
            this.isEditing.meiosContatos = true;
          } else if (id === 'collapseAddressInfo') {
            this.isEditingEndereco = true;
          }
        });
      }

      console.log('isEditMode agora é:', this.isEditMode);
      console.log('isAnyEditing agora é:', this.isAnyEditing);
    },

    // Método para lidar com o evento edit-mode-active do componente Tratamento
    handleEditModeActive(isActive) {
      console.log('handleEditModeActive chamado com:', isActive);
      this.editModeActive = isActive;

      // A classe edit-mode será aplicada ao BackButton via binding :class
      // O componente BackButton vai verificar essa classe e se ocultar quando necessário
    },

    // New toggle methods for each block
    toggleEditPessoal() {
      this.isEditingPessoal = !this.isEditingPessoal;
      this.isEditMode = this.isEditingPessoal;
    },
    toggleEditResponsavel() {
      this.isEditingResponsavel = !this.isEditingResponsavel;
      this.isEditMode = this.isEditingResponsavel;
    },
    toggleEditEndereco() {
      this.isEditingEndereco = !this.isEditingEndereco;
      this.isEditMode = this.isEditingEndereco;
    },

    // New methods to check if blocks are mostly filled
    checkFilledPessoal() {
      // Consider fields in "Informações pessoais"
      const fields = [
        this.paciente.dentista_id,
        this.paciente.clinica && this.paciente.clinica.id,
        this.paciente.data_nascimento,
        this.paciente.como_conheceu,
        this.paciente.rg,
        this.paciente.nome_mae,
        this.paciente.cpf,
        this.paciente.nome_pai,
      ];
      return this.isMostlyFilled(fields);
    },
    checkFilledResponsavel() {
      // Consider fields in "Informações do responsável"
      const fields = [
        this.paciente.responsavel_nome,
        this.paciente.responsavel_rg,
        this.paciente.responsavel_cpf,
      ];
      return this.isMostlyFilled(fields);
    },
    checkFilledEndereco() {
      // Consider fields in "Endereço"
      const fields = [
        this.paciente.endereco_cep,
        this.paciente.endereco_logradouro,
        this.paciente.endereco_numero,
        this.paciente.endereco_complemento,
        this.paciente.endereco_cidade,
        this.paciente.endereco_estado,
      ];
      return this.isMostlyFilled(fields);
    },

    // Helper method to determine if majority of fields are filled
    isMostlyFilled(fields) {
      const total = fields.length;
      const filledCount = fields.filter((f) => f !== null && f !== undefined && f !== "").length;
      // Consider majority filled if more than half fields are filled
      return filledCount >= Math.ceil(total / 2);
    },
    clearNovoContato() {
      this.novoContato.contato = "";
      this.novoContato.descricao = "";
    },
    getContatoHref(tipo, contato) {
      switch (tipo) {
        case "email":
          return `mailto:${contato}`;
        case "whatsapp":
          return `https://wa.me/55${contato.replace(/\D+/g, "")}`;
        case "telefone":
        case "celular":
          return `tel:${contato.replace(/\D+/g, "")}`;
        default:
          return "#";
      }
    },

    excluirContato(id, tipo) {
      if (tipo == "whatsapp") tipo = "WhatsApp";
      else if (tipo == "email") tipo = "e-mail";

      cSwal.cConfirm("Deseja realmente excluir este " + tipo + "?", async () => {
        cSwal.loading("Excluindo contato...");
        const del = await excluirMeioContato(id);
        if (del) {
          await this.refreshPaciente({ onlyContatos: true });
          cSwal.loaded();
        } else {
          cSwal.loaded();
          cSwal.cError("Ocorreu um erro ao excluir o meio de contato");
        }
      });
    },

    async adicionarContato() {
      cSwal.loading("Adicionando contato...");
      const add = await adicionarMeioContato(this.paciente.id, this.novoContato);

      if (add) {
        await this.refreshPaciente({ onlyContatos: true });
        cSwal.loaded();
        this.clearNovoContato();
      } else {
        cSwal.loaded();
        cSwal.cError("Ocorreu um erro ao salvar o contato.");
      }
    },
    selectMeioContato(tipo) {
      // Update the parent's novoContato object to stay in sync with the child component
      this.novoContato.tipo = tipo;
      // The focus is now handled in the child component
    },
    confirmSavePaciente() {
      cSwal.cConfirm("Deseja realmente salvar as alterações no banco de dados?", async () => {
        const update = await updatePaciente(this.paciente);

        if (update) {
          cSwal.cSuccess("As alterações foram salvas no banco de dados.");
          // Atualizar o originalPaciente para refletir as mudanças salvas
          this.originalPaciente = JSON.parse(JSON.stringify(this.paciente));
          this.changedFields.clear();

          // Limpar cache local após salvar no banco
          this.clearLocalCache();

          // Desativar todos os modos de edição
          this.isEditingPessoal = false;
          this.isEditingResponsavel = false;
          this.isEditingEndereco = false;
          this.isEditing.meiosContatos = false;

          // Não precisamos fazer o refreshPaciente completo, apenas atualizar o originalPaciente
          // await this.refreshPaciente();
        } else {
          cSwal.cError("Ocorreu um erro ao salvar as alterações.");
        }
      });
    },

    // ===== SISTEMA DE AUTO-SAVE =====

    detectChangesAndScheduleAutoSave() {
      // Limpar timer anterior se existir
      if (this.autoSaveTimer) {
        clearTimeout(this.autoSaveTimer);
      }

      // Verificar se realmente há mudanças
      if (!this.hasPendingChanges) {
        return;
      }

      // Agendar auto-save
      this.autoSaveTimer = setTimeout(() => {
        this.performAutoSave();
      }, this.autoSaveDelay);
    },

    async performAutoSave() {
      if (!this.hasPendingChanges || this.isAutoSaving) {
        return;
      }

      this.isAutoSaving = true;

      try {
        // Salvar temporariamente no localStorage em vez do banco
        this.saveToLocalCache();
        this.lastAutoSaveTime = new Date();

        // Mostrar toast discreto de confirmação
        this.showAutoSaveToast();

      } catch (error) {
        console.error('Erro no auto-save temporário:', error);
      } finally {
        this.isAutoSaving = false;
      }
    },

    showAutoSaveToast() {
      showAutoSaveToast('Rascunho salvo automaticamente');
    },

    // ===== SISTEMA DE CACHE LOCAL =====

    saveToLocalCache(showToast = true) {
      if (!this.paciente?.id) return;

      const cacheData = {
        paciente: JSON.parse(JSON.stringify(this.paciente)),
        originalPaciente: JSON.parse(JSON.stringify(this.originalPaciente)),
        timestamp: Date.now(),
        changedFields: Array.from(this.changedFields),
        // Salvar estados de edição para restaurar depois
        editingStates: {
          isEditingPessoal: this.isEditingPessoal,
          isEditingResponsavel: this.isEditingResponsavel,
          isEditingEndereco: this.isEditingEndereco,
          isEditingMeiosContatos: this.isEditing.meiosContatos
        }
        // Aba ativa não é mais salva para evitar mudanças inesperadas
      };

      try {
        localStorage.setItem(this.patientCacheKey, JSON.stringify(cacheData));

        // Adicionar ao sistema global de drafts (apenas se não estamos restaurando)
        if (!this.isRestoringFromCache) {
          this.globalDrafts.addOrUpdateDraft({
            patientId: this.paciente.id,
            idFicha: this.paciente.id_ficha || this.paciente.id, // ID da ficha para URL correta
            patientName: this.paciente.nome,
            changesCount: this.changedFields.size,
            lastModified: Date.now(),
            cacheKey: this.patientCacheKey,
            patientClinicId: this.paciente.clinica?.id // ID da clínica
          });

          // Mostrar toast com debounce se solicitado
          if (showToast) {
            this.debouncedToast();
          }
        } else {
          console.log('💾 Salvamento no cache local durante restauração - draft global não atualizado');
        }

      } catch (error) {
        console.warn('Erro ao salvar no cache local:', error);
      }
    },

    // Toast com debounce para evitar spam
    debouncedToast() {
      // Limpar timer anterior se existir
      if (this.toastTimer) {
        clearTimeout(this.toastTimer);
      }

      // Criar novo timer para mostrar toast após 1.5s de inatividade
      this.toastTimer = setTimeout(() => {
        this.showAutoSaveToast();
      }, 1500); // 1.5 segundos
    },

    loadFromLocalCache() {
      if (!this.paciente?.id) return null;

      try {
        const cached = localStorage.getItem(this.patientCacheKey);
        if (cached) {
          const cacheData = JSON.parse(cached);
          // Verificar se o cache não é muito antigo (máximo 24 horas)
          const maxAge = 24 * 60 * 60 * 1000; // 24 horas em ms
          if (Date.now() - cacheData.timestamp < maxAge) {
            return cacheData;
          }
        }
      } catch (error) {
        console.warn('Erro ao carregar do cache local:', error);
      }

      return null;
    },

    clearLocalCache() {
      if (!this.paciente?.id) return;

      try {
        localStorage.removeItem(this.patientCacheKey);

        // Remover também do sistema global de drafts
        this.globalDrafts.removeDraft(this.paciente.id);

      } catch (error) {
        console.warn('Erro ao limpar cache local:', error);
      }
    },

    checkAndRestoreFromCache() {
      console.log('🔍 Verificando cache local e global para sincronização...');

      // 1. Verificar cache local primeiro
      const cached = this.loadFromLocalCache();

      // 2. Verificar se existe draft global para este paciente
      const globalDraft = this.globalDrafts.getDraft(this.paciente?.id);

      console.log('📊 Estado dos caches:', {
        localCache: cached ? `${cached.changedFields.length} alterações` : 'não encontrado',
        globalDraft: globalDraft ? `${globalDraft.changesCount} alterações` : 'não encontrado'
      });

      // 3. Determinar qual cache usar (priorizar o mais recente)
      let finalCache = null;

      if (cached && globalDraft) {
        // Ambos existem - usar o mais recente
        const localTime = cached.timestamp || 0;
        const globalTime = globalDraft.lastModified || 0;

        if (localTime >= globalTime) {
          finalCache = cached;
          console.log('✅ Usando cache local (mais recente)');
        } else {
          // Cache global mais recente, mas precisamos do cache local para dados completos
          // Tentar recarregar do cache global
          const globalCacheData = localStorage.getItem(globalDraft.cacheKey);
          if (globalCacheData) {
            finalCache = JSON.parse(globalCacheData);
            console.log('✅ Usando cache global (mais recente)');
          } else {
            finalCache = cached;
            console.log('⚠️ Cache global sem dados, usando local');
          }
        }
      } else if (cached) {
        finalCache = cached;
        console.log('✅ Usando cache local (único disponível)');
      } else if (globalDraft) {
        // Só existe draft global - tentar carregar dados
        const globalCacheData = localStorage.getItem(globalDraft.cacheKey);
        if (globalCacheData) {
          finalCache = JSON.parse(globalCacheData);
          console.log('✅ Usando cache global (único disponível)');
        } else {
          console.log('❌ Draft global sem dados - limpando draft órfão');
          this.globalDrafts.removeDraft(this.paciente?.id);
        }
      }

      // 4. Restaurar se há cache válido
      if (finalCache && finalCache.changedFields.length > 0) {
        console.log(`🔄 Restaurando ${finalCache.changedFields.length} alterações`);

        // Definir flag para evitar auto-save durante restauração
        this.isRestoringFromCache = true;

        // Restaurar dados do cache automaticamente
        this.paciente = { ...this.paciente, ...finalCache.paciente };
        this.changedFields = new Set(finalCache.changedFields);

        // Ativar modo de edição nos blocos que têm alterações
        this.activateEditingModeForChangedFields(finalCache.changedFields);

        // Mostrar modal informativo sobre alterações pendentes
        this.showPendingChangesModal(finalCache);

        // Destacar campos alterados após restaurar
        this.$nextTick(() => {
          setTimeout(() => {
            this.highlightRestoredFields();

            // Resetar flag após completar a restauração
            this.isRestoringFromCache = false;
            console.log('🔄 Restauração concluída - flag resetada');
          }, 500);
        });

        // Sincronizar os dois sistemas se necessário
        this.syncCaches(finalCache);

        console.log(`✅ Rascunho restaurado automaticamente: ${finalCache.changedFields.length} alterações`);
      } else {
        console.log('📭 Nenhum cache válido encontrado');
        // Garantir que a flag está resetada mesmo sem cache
        this.isRestoringFromCache = false;
      }
    },

    // Sincronizar caches local e global
    syncCaches(cacheData) {
      try {
        // Garantir que o cache local está atualizado
        localStorage.setItem(this.patientCacheKey, JSON.stringify(cacheData));

        // Garantir que o draft global está atualizado (apenas se não estamos restaurando)
        if (!this.isRestoringFromCache) {
          this.globalDrafts.addOrUpdateDraft({
            patientId: this.paciente.id,
            idFicha: this.paciente.id_ficha || this.paciente.id,
            patientName: this.paciente.nome,
            changesCount: cacheData.changedFields.length,
            lastModified: cacheData.timestamp || Date.now(),
            cacheKey: this.patientCacheKey,
            patientClinicId: this.paciente.clinica?.id
          });
        } else {
          console.log('🔄 Sincronização de draft global ignorada durante restauração');
        }

        console.log('🔄 Caches sincronizados com sucesso');
      } catch (error) {
        console.error('❌ Erro ao sincronizar caches:', error);
      }
    },

    showPendingChangesModal(cached) {
      const changesHtml = this.generateChangesPreview(cached);
      const count = cached.changedFields.length;

      cSwal.fire({
        title: 'Alterações pendentes',
        html: `
          <div class="pending-changes-modal">
            <div class="alert alert-warning mb-3" style="background-color: #fff3cd; border-color: #ffeaa7; color: #f8f9fa;">
              <i class="fas fa-exclamation-triangle me-2"></i>
              Este paciente possui <strong>${count} ${this.pluralizeChanges(count)}</strong> não ${this.pluralizeSaved(count)}.
            </div>

            <div class="changes-preview">
              <!--h6 class="mb-3">Alterações encontradas:</h6-->
              <div class="compact-changes-table">
                ${changesHtml}
              </div>
            </div>

            <div class="text-muted mt-3">
              <small>
                <i class="fas fa-clock me-1"></i>
                Última modificação: ${new Date(cached.timestamp).toLocaleString('pt-BR')}
              </small>
            </div>

            <p class="mt-4">
              <i class="fas fa-info-circle me-1"></i>
              As alterações foram restauradas automaticamente.
            </p>
          </div>

          <style>
            .compact-changes-table {
              max-height: 300px;
              overflow-y: auto;
              border: 1px solid #dee2e6;
              border-radius: 6px;
            }

            .compact-changes-table .changes-table {
              width: 100%;
              margin: 0;
              font-size: 0.85rem;
            }

            .compact-changes-table .changes-table th {
              padding: 8px 12px;
              font-weight: 600;
              font-size: 0.8rem;
              border-bottom: 1px solid #dee2e6;
            }

            .compact-changes-table .changes-table td {
              padding: 6px 12px;
              border-bottom: 1px solid #f1f3f4;
              vertical-align: middle;
            }

            .compact-changes-table .category-header {
              background: #e9ecef;
              font-weight: 600;
              font-size: 0.8rem;
              padding: 8px 12px;
            }

            .compact-changes-table .old-value,
            .compact-changes-table .new-value {
              padding: 2px 6px;
              border-radius: 3px;
              font-size: 0.8rem;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }

            .compact-changes-table .old-value {
              background: #fff3cd;
              color: #856404;
              border: 1px solid #ffeaa7;
            }

            .compact-changes-table .new-value {
              // background: #d1edff;
              // color: #0c5460;
              background: #dff0d8;
              color: #28a745;
              border: 1px solid #bee5eb;
              font-weight: 600;
            }
          </style>
        `,
        icon: null,
        confirmButtonText: 'Entendi',
        confirmButtonColor: '#007bff',
        customClass: {
          popup: 'pending-changes-info-popup'
        },
        width: '600px'
      });
    },

    // Ativar modo de edição nos blocos que contêm alterações
    activateEditingModeForChangedFields(changedFields) {
      const personalFields = ['nome', 'cpf', 'rg', 'data_nascimento', 'como_conheceu', 'nome_mae', 'nome_pai', 'observacoes', 'dentista_id'];
      const responsibleFields = ['responsavel_nome', 'responsavel_cpf', 'responsavel_rg'];
      const addressFields = ['endereco_cep', 'endereco_estado', 'endereco_cidade', 'endereco_logradouro', 'endereco_numero', 'endereco_complemento'];
      const contactFields = ['contatos']; // Assumindo que contatos são tratados separadamente

      let hasPersonalChanges = false;
      let hasResponsibleChanges = false;
      let hasAddressChanges = false;
      let hasContactChanges = false;

      changedFields.forEach(field => {
        if (personalFields.includes(field)) {
          hasPersonalChanges = true;
        } else if (responsibleFields.includes(field)) {
          hasResponsibleChanges = true;
        } else if (addressFields.includes(field)) {
          hasAddressChanges = true;
        } else if (contactFields.includes(field)) {
          hasContactChanges = true;
        }
      });

      // Ativar modo de edição apenas nos blocos que têm alterações
      if (hasPersonalChanges) {
        this.isEditingPessoal = true;
      }
      if (hasResponsibleChanges) {
        this.isEditingResponsavel = true;
      }
      if (hasAddressChanges) {
        this.isEditingEndereco = true;
      }
      if (hasContactChanges) {
        this.isEditing.meiosContatos = true;
      }
    },

    generateChangesPreview(cached) {
      // Mapeamento de campos para nomes amigáveis
      const fieldMapping = {
        'nome': { label: 'Nome completo', category: 'Informações pessoais' },
        'cpf': { label: 'CPF', category: 'Informações pessoais' },
        'rg': { label: 'RG', category: 'Informações pessoais' },
        'data_nascimento': { label: 'Data de nascimento', category: 'Informações pessoais' },
        'como_conheceu': { label: 'Como conheceu a clínica', category: 'Informações pessoais' },
        'nome_mae': { label: 'Nome da mãe', category: 'Informações pessoais' },
        'nome_pai': { label: 'Nome do pai', category: 'Informações pessoais' },
        'observacoes': { label: 'Observações', category: 'Informações pessoais' },
        'dentista_id': { label: 'Dentista responsável', category: 'Informações pessoais' },
        'responsavel_nome': { label: 'Nome do responsável', category: 'Informações do responsável' },
        'responsavel_cpf': { label: 'CPF do responsável', category: 'Informações do responsável' },
        'responsavel_rg': { label: 'RG do responsável', category: 'Informações do responsável' },
        'endereco_cep': { label: 'CEP', category: 'Endereço' },
        'endereco_estado': { label: 'Estado', category: 'Endereço' },
        'endereco_cidade': { label: 'Cidade', category: 'Endereço' },
        'endereco_logradouro': { label: 'Logradouro', category: 'Endereço' },
        'endereco_numero': { label: 'Número', category: 'Endereço' },
        'endereco_complemento': { label: 'Complemento', category: 'Endereço' }
      };

      // Agrupar alterações por categoria
      const changesByCategory = {};

      cached.changedFields.forEach(fieldName => {
        const fieldInfo = fieldMapping[fieldName];
        if (fieldInfo) {
          const category = fieldInfo.category;
          if (!changesByCategory[category]) {
            changesByCategory[category] = [];
          }

          // Obter valores antigo e novo
          const oldValue = this.originalPaciente[fieldName] || '';
          const newValue = cached.paciente[fieldName] || '';

          changesByCategory[category].push({
            field: fieldName,
            label: fieldInfo.label,
            oldValue: this.formatFieldValue(fieldName, oldValue),
            newValue: this.formatFieldValue(fieldName, newValue)
          });
        }
      });

      // Gerar tabela HTML limpa
      let html = '<table class="changes-table">';
      html += `
        <thead>
          <tr>
            <th>Campo alterado</th>
            <th style="color: #856404;">Valor anterior</th>
            <th style="color: #0c5460;">Novo valor</th>
          </tr>
        </thead>
        <tbody>
      `;

      Object.keys(changesByCategory).forEach(category => {
        const changes = changesByCategory[category];

        // Header da categoria
        html += `
          <tr class="table-secondary">
            <td colspan="3" class="category-header">
              <strong>${category} (${changes.length} ${this.pluralizeChanges(changes.length)})</strong>
            </td>
          </tr>
        `;

        // Linhas das alterações
        changes.forEach(change => {
          html += `
            <tr class="change-row">
              <td class="field-name">${change.label}</td>
              <td class="old-value">${change.oldValue || '<em>vazio</em>'}</td>
              <td class="new-value">${change.newValue || '<em>vazio</em>'}</td>
            </tr>
          `;
        });
      });

      html += '</tbody></table>';
      return html;
    },

    formatFieldValue(fieldName, value) {
      if (!value || value === '') return '';

      // Formatações específicas por tipo de campo
      switch (fieldName) {
        case 'cpf':
          return value.replace(/(\d{3})(\d{3})(\d{3})(\d{2})/, '$1.$2.$3-$4');
        case 'data_nascimento': {
          if (value.includes('-')) {
            const [year, month, day] = value.split('-');
            return `${day}/${month}/${year}`;
          }
          return value;
        }
        case 'endereco_cep':
          return value.replace(/(\d{5})(\d{3})/, '$1-$2');
        case 'dentista_id': {
          const dentista = this.dentistas.find(d => d.id == value);
          return dentista ? dentista.nome : value;
        }
        default:
          // Truncar valores muito longos
          return value.length > 50 ? value.substring(0, 47) + '...' : value;
      }
    },

    // ===== HELPERS DE PLURALIZAÇÃO =====

    pluralizeChanges(count) {
      return count === 1 ? 'alteração' : 'alterações';
    },

    pluralizeSaved(count) {
      return count === 1 ? 'salva' : 'salvas';
    },

    // Auto-save com debounce
    debouncedAutoSave() {
      // Limpar timer anterior se existir
      if (this.autoSaveTimer) {
        clearTimeout(this.autoSaveTimer);
      }

      // Criar novo timer
      this.autoSaveTimer = setTimeout(() => {
        if (this.changedFields.size > 0) {
          // saveToLocalCache já vai mostrar o toast com debounce
          this.saveToLocalCache();
        }
      }, 500); // 500ms de debounce
    },

    // ===== MÉTODOS DE CANCELAR EDIÇÃO =====

    cancelEditPessoal() {
      // Verificar se há alterações nos campos pessoais
      const personalFields = ['nome', 'cpf', 'rg', 'data_nascimento', 'como_conheceu', 'nome_mae', 'nome_pai', 'observacoes', 'dentista_id'];
      const hasChanges = personalFields.some(field => this.changedFields.has(field));

      if (!hasChanges) {
        // Sem alterações, cancelar diretamente
        this.isEditingPessoal = false;
        return;
      }

      // Com alterações, pedir confirmação
      cSwal.cConfirm(
        "Deseja realmente cancelar a edição? Todas as alterações não salvas serão perdidas.",
        () => {
          // Restaurar valores originais dos campos pessoais
          personalFields.forEach(field => {
            if (this.originalPaciente[field] !== undefined) {
              this.paciente[field] = this.originalPaciente[field];
              this.changedFields.delete(field);
            }
          });

          // Desativar modo de edição
          this.isEditingPessoal = false;

          // Atualizar cache (sem toast pois é cancelamento)
          this.saveToLocalCache(false);
        }
      );
    },

    cancelEditResponsavel() {
      // Verificar se há alterações nos campos do responsável
      const responsibleFields = ['responsavel_nome', 'responsavel_cpf', 'responsavel_rg'];
      const hasChanges = responsibleFields.some(field => this.changedFields.has(field));

      if (!hasChanges) {
        // Sem alterações, cancelar diretamente
        this.isEditingResponsavel = false;
        return;
      }

      // Com alterações, pedir confirmação
      cSwal.cConfirm(
        "Deseja realmente cancelar a edição? Todas as alterações não salvas serão perdidas.",
        () => {
          // Restaurar valores originais dos campos do responsável
          responsibleFields.forEach(field => {
            if (this.originalPaciente[field] !== undefined) {
              this.paciente[field] = this.originalPaciente[field];
              this.changedFields.delete(field);
            }
          });

          // Desativar modo de edição
          this.isEditingResponsavel = false;

          // Atualizar cache (sem toast pois é cancelamento)
          this.saveToLocalCache(false);
        }
      );
    },

    cancelEditEndereco() {
      // Verificar se há alterações nos campos de endereço
      const addressFields = ['endereco_cep', 'endereco_estado', 'endereco_cidade', 'endereco_logradouro', 'endereco_numero', 'endereco_complemento'];
      const hasChanges = addressFields.some(field => this.changedFields.has(field));

      if (!hasChanges) {
        // Sem alterações, cancelar diretamente
        this.isEditingEndereco = false;
        return;
      }

      // Com alterações, pedir confirmação
      cSwal.cConfirm(
        "Deseja realmente cancelar a edição? Todas as alterações não salvas serão perdidas.",
        () => {
          // Restaurar valores originais dos campos de endereço
          addressFields.forEach(field => {
            if (this.originalPaciente[field] !== undefined) {
              this.paciente[field] = this.originalPaciente[field];
              this.changedFields.delete(field);
            }
          });

          // Desativar modo de edição
          this.isEditingEndereco = false;

          // Atualizar cache (sem toast pois é cancelamento)
          this.saveToLocalCache(false);
        }
      );
    },

    cancelEditContatos() {
      cSwal.cConfirm(
        "Deseja realmente cancelar a edição? Todas as alterações não salvas serão perdidas.",
        () => {
          // Para contatos, vamos apenas desativar o modo de edição
          // (a lógica de contatos pode ser mais complexa e específica)
          this.isEditing.meiosContatos = false;

          // Se não há mais alterações, limpar cache
          if (this.changedFields.size === 0) {
            this.clearLocalCache();
          } else {
            this.saveToLocalCache(false);
          }
        }
      );
    },

    // Handler para quando draft é salvo e removido via modal global
    handleDraftSavedAndRemoved(event) {
      const data = event.detail;

      console.log('🔄 Draft salvo e removido via modal:', {
        eventPatientId: data.patientId,
        currentPatientId: this.paciente?.id,
        match: data.patientId == this.paciente?.id
      });

      // Verificar se é o paciente atual
      if (data.patientId == this.paciente?.id) {
        // console.log('✅ É o paciente atual - finalizando edição');

        // Atualizar dados do paciente com os dados salvos
        if (data.savedPatientData) {
          Object.assign(this.paciente, data.savedPatientData);
        }

        // Atualizar referência original
        this.originalPaciente = JSON.parse(JSON.stringify(this.paciente));

        // Limpar alterações pendentes
        this.changedFields.clear();

        // FORÇAR saída do modo de edição
        this.isEditingPessoal = false;
        this.isEditingResponsavel = false;
        this.isEditingEndereco = false;
        this.isEditing.meiosContatos = false;

        // Limpar cache local
        this.clearLocalCache();

        // Forçar atualização da interface
        this.$nextTick(() => {
          this.$forceUpdate();
        });

        // console.log('✅ Edição finalizada com sucesso');
      }
    },

    // Determinar se devemos salvar draft na navegação
    shouldBlockNavigation(to, from) {
      // Permitir navegação dentro da mesma página (mudança de hash/query)
      if (to.path === from.path) {
        return false;
      }

      // Verificar se a rota de destino é relacionada ao mesmo paciente
      const isSamePatient = to.params?.id_ficha === this.$route.params?.id_ficha;

      // Verificar se é navegação para uma página completamente diferente
      const isLeavingPatientArea = !to.path.includes('/paciente');

      // Salvar draft se estiver saindo da área do paciente OU mudando de paciente
      return isLeavingPatientArea || !isSamePatient;
    },

    highlightChangedFields() {
      // Destacar campos que foram alterados
      this.changedFields.forEach(fieldName => {
        const field = document.querySelector(`[name="${fieldName}"], #${fieldName}`);
        if (field) {
          field.classList.add('field-changed');
          setTimeout(() => {
            field.classList.remove('field-changed');
          }, 3000);
        }
      });
    },

    highlightRestoredFields() {
      // Aguardar um pouco para garantir que os campos estejam renderizados
      setTimeout(() => {
        // Destacar campos que foram restaurados do cache com animação especial
        this.changedFields.forEach(fieldName => {
          // Tentar múltiplos seletores para encontrar o campo
          const selectors = [
            `[name="${fieldName}"]`,
            `#${fieldName}`,
            `input[name="${fieldName}"]`,
            `select[name="${fieldName}"]`,
            `textarea[name="${fieldName}"]`,
            `.form-control[name="${fieldName}"]`
          ];

          let field = null;
          for (const selector of selectors) {
            field = document.querySelector(selector);
            if (field) break;
          }

          if (field) {
            console.log(`Destacando campo: ${fieldName}`, field);
            field.classList.add('field-restored', 'field-pending');

            // Criar efeito de "pulse" suave
            setTimeout(() => {
              field.classList.add('field-restored-pulse');
            }, 100);

            // Remover apenas o destaque de restauração após 8 segundos
            // Manter field-pending até que seja salvo ou descartado
            setTimeout(() => {
              field.classList.remove('field-restored', 'field-restored-pulse');
            }, 8000);
          } else {
            console.warn(`Campo não encontrado: ${fieldName}`);
          }
        });

        // Scroll suave para o primeiro campo alterado
        if (this.changedFields.size > 0) {
          const firstField = Array.from(this.changedFields)[0];
          const selectors = [
            `[name="${firstField}"]`,
            `#${firstField}`,
            `input[name="${firstField}"]`,
            `select[name="${firstField}"]`,
            `textarea[name="${firstField}"]`
          ];

          let firstElement = null;
          for (const selector of selectors) {
            firstElement = document.querySelector(selector);
            if (firstElement) break;
          }

          if (firstElement) {
            setTimeout(() => {
              firstElement.scrollIntoView({
                behavior: 'smooth',
                block: 'center',
                inline: 'nearest'
              });
            }, 1000);
          }
        }
      }, 200); // Aguardar 200ms para renderização
    },

    showInfoToast(message) {
      showInfoToast(message, { duration: 5000 });
    },

    async handleFormLinkBtn() {
      if (this.possuiWhatsapp) this.enviarFormulario();
      else await this.copiarLink();
    },

    confirmarExcluirPaciente() {
      cSwal.cConfirm(
        "Tem certeza que deseja excluir este paciente? Esta ação não pode ser desfeita.",
        () => this.excluirPaciente(),
        {
          title: "Excluir paciente",
          icon: "warning",
          confirmButtonText: "Sim, excluir",
          cancelButtonText: "Cancelar",
        }
      );
    },

    async excluirPaciente() {
      cSwal.loading("Excluindo paciente...");

      try {
        const resultado = await excluirPaciente(this.paciente.id);

        if (resultado) {
          cSwal.loaded();
          cSwal.cSuccess("Paciente excluído com sucesso.");
          // Redirecionar para a lista de pacientes
          this.$router.push({ name: "Pacientes" });
        } else {
          cSwal.loaded();
          cSwal.cError("Não foi possível excluir o paciente. Tente novamente.");
        }
      } catch (error) {
        console.error("Erro ao excluir paciente:", error);
        cSwal.loaded();
        cSwal.cError("Ocorreu um erro ao excluir o paciente.");
      }
    },
    async copiarLink() {
      const link = this.getFichaInicialLink();

      if (!navigator.clipboard) {
        cSwal.cInfo(
          "Link da ficha de avaliação inicial para o paciente<br><b>" +
            this.paciente.nome +
            "</b>:<br><br><b>" +
            link +
            "</b>"
        );
        return false;
      }

      // Copy link to clipboard
      await navigator.clipboard
        .writeText(link)
        .then(() => {
          console.log("Link copied to clipboard!");
        })
        .catch((error) => {
          console.error("Error copying link:", error);
        });

      cSwal.cAlert("O link do formulário foi copiado.");
    },
    enviarFormulario() {
      const whatsappNumber = this.whatsappNumero;
      const phoneNumber = whatsappNumber.replace(/\D+/g, ""); // extract only numbers
      if (phoneNumber.length !== 11) {
        // show error message
        cSwal.cAlert("Número de WhatsApp inválido. Por favor, verifique o número.");
        return;
      }
      const link = this.getFichaInicialLink();
      const whatsappLink = `https://wa.me/55${phoneNumber}?text=Olá, bem-vindo a clínica! Por favor, preencha nosso formulário para lhe conhecermos melhor: ${link}`;
      window.open(whatsappLink, "_blank"); // open in new tab
    },

    validarCep(cep) {
      if (!cep) return false;

      return /^\d{8}$/.test(cep.replace(/[^\d]+/g, ""));
    },

    async getEndereco(event) {
      clearTimeout(this.timeout);
      this.timeout = setTimeout(async () => {
        var cep = event.target.value;
        cep = this.paciente.endereco_cep;

        if (!this.validarCep(cep)) return false;

        const enderecoInfo = await getEnderecoByCep(cep);
        if (!enderecoInfo) return false;

        this.paciente.endereco_logradouro = enderecoInfo.street;
        this.paciente.endereco_cidade = enderecoInfo.city;
        this.paciente.endereco_estado = enderecoInfo.state;

        if (!this.paciente.endereco_numero) this.$refs.endereco_numero.getInput().focus();
      }, 50);
    },

    updatePacienteField({ field, value }) {
      // Handle special case for novoContato fields
      if (field.startsWith('novoContato.')) {
        const property = field.split('.')[1];
        this.novoContato[property] = value;
        return;
      }

      // Rastrear campo alterado para destacar posteriormente
      this.changedFields.add(field);

      // Use lodash's set or a similar approach to handle nested paths
      if (field.includes('.')) {
        const parts = field.split('.');
        let obj = this.paciente;
        for (let i = 0; i < parts.length - 1; i++) {
          if (!obj[parts[i]]) {
            obj[parts[i]] = {};
          }
          obj = obj[parts[i]];
        }
        obj[parts[parts.length - 1]] = value;
      } else {
        this.paciente[field] = value;
      }

      // Forçar a detecção de mudanças criando um novo objeto paciente
      // Isso é necessário porque o Vue pode não detectar mudanças em objetos aninhados
      this.paciente = { ...this.paciente };
    },

    getContatoIcon(type) {
      var icon = null;
      switch (type) {
        case "whatsapp":
          icon = ["fab", "whatsapp"];
          break;
        case "celular":
          icon = ["fas", "mobile-screen-button"];
          break;
        case "telefone":
          icon = "mdi-phone";
          break;
        case "email":
          icon = ["fas", "envelope"];
          break;
      }
      return icon;
    },

    getInfoIcon(nivel) {
      var icon = null;
      switch (nivel) {
        case "positivo":
          icon = "thumbs-up";
          break;
        case "neutro":
          icon = "info-circle";
          break;
        case "atencao":
          icon = "circle-exclamation";
          break;
        case "negativo":
          icon = "thumbs-down";
          break;
      }

      return icon;
    },

    setProfileTab(tab) {
      this.activeProfileTab = tab;
    },

    openTab(tab) {
      this.activeTab = tab;
      setTimeout(() => {
        window.dispatchEvent(new Event("resize"));
      }, 100);
    },

    // Aplicar estilo de campo pendente em tempo real
    applyPendingFieldStyles() {
      // Remover classe de todos os campos primeiro
      document.querySelectorAll('.field-pending').forEach(field => {
        field.classList.remove('field-pending');
      });

      // Aplicar classe apenas nos campos que têm alterações
      this.changedFields.forEach(fieldName => {
        const selectors = [
          `[name="${fieldName}"]`,
          `#${fieldName}`,
          `input[name="${fieldName}"]`,
          `select[name="${fieldName}"]`,
          `textarea[name="${fieldName}"]`,
          `.form-control[name="${fieldName}"]`
        ];

        let field = null;
        for (const selector of selectors) {
          field = document.querySelector(selector);
          if (field) break;
        }

        if (field) {
          field.classList.add('field-pending');
        }
      });
    },
    async refreshPaciente(options) {
      this.isEditing.meiosContatos = false;
      await this.getPacienteDetails(this.paciente.id_ficha, clinicaSlug, options);
    },
    async getPacienteDetails(idFicha, clinicaSlug, options) {
      this.isLoading.paciente = true;

      options = {
        onlyContatos: false,
        ...options,
      };
      const paciente = await getPaciente(idFicha, clinicaSlug);
      if (paciente && !options.onlyContatos) {
        // Normalizar strings vazias para null antes de definir os objetos
        const normalizedPaciente = this.normalizeEmptyStrings(JSON.parse(JSON.stringify(paciente)));
        this.paciente = normalizedPaciente;
        this.originalPaciente = JSON.parse(JSON.stringify(normalizedPaciente));

        // After loading paciente, check if blocks are filled
        this.isFilledPessoal = this.checkFilledPessoal();
        this.isFilledResponsavel = this.checkFilledResponsavel();
        this.isFilledEndereco = this.checkFilledEndereco();

        // Set edit mode based on filled status: if filled, readonly (edit mode off)
        this.isEditingPessoal = !this.isFilledPessoal;
        this.isEditingResponsavel = !this.isFilledResponsavel;
        this.isEditingEndereco = !this.isFilledEndereco;
      }

      else if (paciente && options.onlyContatos) {
        this.paciente.contatos = paciente.contatos;
        this.originalPaciente = {
          ...this.originalPaciente,
          contatos: paciente.contatos,
        };
      }

      else if (idFicha) {
          cSwal.cError("Ocorreu um erro ao tentar carregar os dados do paciente.");
      }

      this.isLoading.paciente = false;

      const fichaInicial = await getFichaInicial(this.paciente.id);
      if (fichaInicial) {
        this.questoesFichaInicial = fichaInicial.questoes;
        this.dataRespostaFicha = fichaInicial.data_resposta;
      }

      // Carregar faturas do paciente se estiver na aba financeiro
      if (this.activeTab === 'financeiro') {
        this.loadFaturasPaciente();
      }
    },

    // Métodos do Financeiro
    async loadFaturasPaciente() {
      if (!this.paciente.id) return;

      this.loading.faturas = true;
      try {
        const response = await financeiroService.getFaturasPaciente(this.paciente.id);
        this.faturasPaciente = response.data.data;
      } catch (error) {
        console.error('Erro ao carregar faturas do paciente:', error);
        cSwal.cError('Erro ao carregar faturas do paciente');
      } finally {
        this.loading.faturas = false;
      }
    },

    openCreateFaturaModal() {
      this.$refs.financeiroCreateModal.open(this.paciente.id, this.paciente);
    },

    openCreateOrcamentoModal() {
      // TODO: Implementar modal de orçamento separado
      console.log('Modal de orçamento em desenvolvimento');
    },

    openEditFaturaModal(fatura) {
      // TODO: Implementar abertura do modal de edição
      cSwal.cInfo('Modal de edição de fatura em desenvolvimento');
    },

    async deleteFaturaPaciente(faturaId) {
      if (confirm('Tem certeza que deseja cancelar esta fatura?')) {
        try {
          await financeiroService.deleteFatura(faturaId);
          cSwal.cSuccess('Fatura cancelada com sucesso');
          this.loadFaturasPaciente();
        } catch (error) {
          console.error('Erro ao cancelar fatura:', error);
          cSwal.cError('Erro ao cancelar fatura');
        }
      }
    },

    async markFaturaAsPaid(faturaId, paymentData) {
      try {
        await financeiroService.markAsPaid(faturaId, paymentData);
        cSwal.cSuccess('Fatura marcada como paga');
        this.loadFaturasPaciente();
      } catch (error) {
        console.error('Erro ao marcar fatura como paga:', error);
        cSwal.cError('Erro ao marcar fatura como paga');
      }
    },

    generateReceipt(fatura) {
      // TODO: Implementar geração de recibo
      cSwal.cInfo('Geração de recibo em desenvolvimento');
    },

    onFinanceiroSaved() {
      this.loadFaturasPaciente();
    },
  },

  async created() {
    clinicaSlug = this.$route.params.clinica_slug || null;
    pacienteIdFicha = this.$route.params.id_ficha;
    this.getPacienteDetails(pacienteIdFicha, clinicaSlug);
  },

  async mounted() {
    this.refreshClinicas()
    setNavPills();
    setTooltip();
    this.$store.state.isAbsolute = true;

    // Listener para salvamento via modal global
    window.addEventListener('draftSavedAndRemoved', this.handleDraftSavedAndRemoved);

    this.refreshDentistas();

    // Verificar cache local após carregar dados do paciente
    this.$nextTick(() => {
      setTimeout(() => {
        console.log(`🚀 Iniciando verificação de cache para paciente ${this.paciente?.nome} (ID: ${this.paciente?.id})`);
        this.checkAndRestoreFromCache();
      }, 1000); // Aguardar carregamento completo
    });
  },

  beforeUnmount() {
    // Limpar timer de auto-save
    if (this.autoSaveTimer) {
      clearTimeout(this.autoSaveTimer);
    }

    // Limpar timer de toast
    if (this.toastTimer) {
      clearTimeout(this.toastTimer);
    }

    // Remover listener
    window.removeEventListener('draftSavedAndRemoved', this.handleDraftSavedAndRemoved);
  },

  // Router guard para detectar navegação com mudanças pendentes
  beforeRouteLeave(to, from, next) {
    // Se há mudanças pendentes (rascunho)
    if (this.hasPendingChanges) {
      // Verificar se devemos bloquear esta navegação
      const shouldBlockNavigation = this.shouldBlockNavigation(to, from);

      if (shouldBlockNavigation) {
        // Salvar rascunho no cache (que automaticamente adiciona ao sistema global)
        this.saveToLocalCache();
      }

      // Sempre permitir navegação - o sistema global de drafts cuidará do resto
      next();
    } else {
      next(); // Permitir navegação se não há mudanças pendentes
    }
  },
};
</script>
